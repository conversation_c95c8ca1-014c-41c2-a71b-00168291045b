"""
Execution service for order management and trade execution
"""

import asyncio
import logging
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import APIError

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import Order, OrderSide, OrderType, OrderStatus, Position
from src.services.storage_service import StorageService


class ExecutionService:
    """Service for executing trades via Alpaca"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.storage = StorageService()
        
        # Initialize Alpaca client
        self.alpaca = tradeapi.REST(
            key_id=settings.APCA_API_KEY_ID,
            secret_key=settings.APCA_API_SECRET_KEY,
            base_url=settings.APCA_API_BASE_URL,
            api_version='v2'
        )
        
        # Order tracking
        self.active_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        
        # Circuit breaker settings
        self.circuit_breaker_active = False
        self.daily_loss_limit = Decimal("1000")  # $1000 daily loss limit
        self.daily_pnl = Decimal("0")
        
        # Background tasks
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
    async def start(self):
        """Start the execution service"""
        self.logger.info("Starting execution service...")
        self.running = True
        
        # Initialize storage
        await self.storage.initialize()
        
        # Verify account access
        try:
            account = self.alpaca.get_account()
            self.logger.info(f"Connected to Alpaca account: {account.status}")
            
            if settings.PAPER_TRADING:
                self.logger.info("Running in PAPER TRADING mode")
            else:
                self.logger.warning("Running in LIVE TRADING mode")
                
        except Exception as e:
            self.logger.error(f"Failed to connect to Alpaca: {e}")
            raise
            
        # Start background tasks
        self.tasks.append(asyncio.create_task(self._order_monitoring_loop()))
        self.tasks.append(asyncio.create_task(self._position_monitoring_loop()))
        
        self.logger.info("Execution service started")
        
    async def stop(self):
        """Stop the execution service"""
        self.logger.info("Stopping execution service...")
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Close storage
        await self.storage.close()
        
        self.logger.info("Execution service stopped")
        
    async def submit_order(self, order: Order) -> Optional[str]:
        """Submit an order to Alpaca"""
        try:
            # Check circuit breaker
            if self.circuit_breaker_active:
                self.logger.warning("Circuit breaker active - order rejected")
                return None
                
            # Validate order
            if not await self._validate_order(order):
                return None
                
            # Generate client order ID if not provided
            if not order.client_order_id:
                order.client_order_id = str(uuid.uuid4())
                
            # Prepare order parameters
            order_params = {
                'symbol': order.symbol,
                'qty': float(order.qty),
                'side': order.side.value,
                'type': order.order_type.value,
                'time_in_force': order.time_in_force,
                'client_order_id': order.client_order_id
            }
            
            # Add price parameters based on order type
            if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                if order.limit_price:
                    order_params['limit_price'] = float(order.limit_price)
                    
            if order.order_type in [OrderType.STOP, OrderType.STOP_LIMIT]:
                if order.stop_price:
                    order_params['stop_price'] = float(order.stop_price)
                    
            # Add bracket order parameters (supports AI-enhanced stops)
            if order.order_class == "bracket":
                order_params['order_class'] = 'bracket'
                if order.take_profit:
                    order_params['take_profit'] = order.take_profit
                    self.logger.info(f"Take profit set at ${order.take_profit.get('limit_price', 'N/A')}")
                if order.stop_loss:
                    order_params['stop_loss'] = order.stop_loss
                    stop_price = order.stop_loss.get('stop_price', 'N/A')
                    self.logger.info(f"AI-enhanced stop loss set at ${stop_price}")

                    # Log if this is an AI-enhanced stop
                    if hasattr(order, 'ai_enhanced') and order.ai_enhanced:
                        self.logger.info("Using AI-enhanced stop loss calculation")
                    
            # Submit order to Alpaca
            alpaca_order = self.alpaca.submit_order(**order_params)
            
            # Update order with Alpaca response
            order.id = alpaca_order.id
            order.status = OrderStatus(alpaca_order.status)
            order.created_at = alpaca_order.created_at
            order.updated_at = alpaca_order.updated_at
            
            # Store order
            self.active_orders[order.id] = order
            await self.storage.store_order(order.dict())
            
            self.logger.info(f"Order submitted: {order.id} - {order.side.value} {order.qty} {order.symbol}")
            
            return order.id
            
        except APIError as e:
            self.logger.error(f"Alpaca API error submitting order: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error submitting order: {e}")
            return None
            
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.alpaca.cancel_order(order_id)
            
            # Update local order status
            if order_id in self.active_orders:
                self.active_orders[order_id].status = OrderStatus.CANCELED
                await self.storage.store_order(self.active_orders[order_id].dict())
                
            self.logger.info(f"Order canceled: {order_id}")
            return True
            
        except APIError as e:
            self.logger.error(f"Error canceling order {order_id}: {e}")
            return False
            
    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get order status"""
        try:
            # Check local cache first
            if order_id in self.active_orders:
                return self.active_orders[order_id].status
                
            # Query Alpaca
            alpaca_order = self.alpaca.get_order(order_id)
            return OrderStatus(alpaca_order.status)
            
        except APIError as e:
            self.logger.error(f"Error getting order status for {order_id}: {e}")
            return None
            
    async def get_positions(self) -> List[Position]:
        """Get current positions"""
        try:
            alpaca_positions = self.alpaca.list_positions()
            
            positions = []
            for pos in alpaca_positions:
                position = Position(
                    symbol=pos.symbol,
                    qty=Decimal(str(pos.qty)),
                    side=pos.side,
                    market_value=Decimal(str(pos.market_value)),
                    cost_basis=Decimal(str(pos.cost_basis)),
                    unrealized_pl=Decimal(str(pos.unrealized_pl)),
                    unrealized_plpc=Decimal(str(pos.unrealized_plpc)),
                    current_price=Decimal(str(pos.current_price)),
                    lastday_price=Decimal(str(pos.lastday_price)),
                    change_today=Decimal(str(pos.change_today))
                )
                positions.append(position)
                
            return positions
            
        except APIError as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
            
    async def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information"""
        try:
            account = self.alpaca.get_account()
            
            return {
                'id': account.id,
                'status': account.status,
                'currency': account.currency,
                'buying_power': float(account.buying_power),
                'cash': float(account.cash),
                'portfolio_value': float(account.portfolio_value),
                'equity': float(account.equity),
                'last_equity': float(account.last_equity),
                'multiplier': int(account.multiplier),
                'day_trade_count': int(account.day_trade_count),
                'daytrade_buying_power': float(account.daytrade_buying_power),
                'regt_buying_power': float(account.regt_buying_power),
                'pattern_day_trader': account.pattern_day_trader,
                'trading_blocked': account.trading_blocked,
                'transfers_blocked': account.transfers_blocked,
                'account_blocked': account.account_blocked
            }
            
        except APIError as e:
            self.logger.error(f"Error getting account info: {e}")
            return None
            
    async def _validate_order(self, order: Order) -> bool:
        """Validate order before submission"""
        try:
            # Check if market is open (for market orders)
            if order.order_type == OrderType.MARKET:
                clock = self.alpaca.get_clock()
                if not clock.is_open:
                    self.logger.warning(f"Market is closed - cannot submit market order for {order.symbol}")
                    return False
                    
            # Check buying power
            account = self.alpaca.get_account()
            buying_power = Decimal(str(account.buying_power))
            
            if order.side in [OrderSide.BUY]:
                estimated_cost = order.qty * (order.limit_price or Decimal("100"))  # Rough estimate
                if estimated_cost > buying_power:
                    self.logger.warning(f"Insufficient buying power for order: {estimated_cost} > {buying_power}")
                    return False
                    
            # Check position limits
            positions = await self.get_positions()
            if len(positions) >= settings.MAX_POSITIONS:
                # Check if this is closing an existing position
                existing_position = next((p for p in positions if p.symbol == order.symbol), None)
                if not existing_position or order.side not in [OrderSide.SELL, OrderSide.COVER]:
                    self.logger.warning(f"Maximum positions limit reached: {len(positions)}")
                    return False
                    
            # Check position size limits
            if order.limit_price:
                position_value = order.qty * order.limit_price
                if position_value > Decimal(str(settings.MAX_POSITION_SIZE)):
                    self.logger.warning(f"Position size too large: {position_value}")
                    return False
                    
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating order: {e}")
            return False
            
    async def _order_monitoring_loop(self):
        """Monitor active orders for status updates"""
        while self.running:
            try:
                # Check status of active orders
                for order_id in list(self.active_orders.keys()):
                    try:
                        alpaca_order = self.alpaca.get_order(order_id)
                        local_order = self.active_orders[order_id]
                        
                        # Update if status changed
                        new_status = OrderStatus(alpaca_order.status)
                        if local_order.status != new_status:
                            local_order.status = new_status
                            local_order.filled_qty = Decimal(str(alpaca_order.filled_qty or 0))
                            local_order.filled_avg_price = Decimal(str(alpaca_order.filled_avg_price or 0))
                            local_order.updated_at = alpaca_order.updated_at
                            
                            # Store updated order
                            await self.storage.store_order(local_order.dict())
                            
                            self.logger.info(f"Order {order_id} status updated to {new_status.value}")
                            
                            # Move to history if order is done
                            if new_status in [OrderStatus.FILLED, OrderStatus.CANCELED, OrderStatus.EXPIRED]:
                                self.order_history.append(local_order)
                                del self.active_orders[order_id]
                                
                    except APIError as e:
                        if "order not found" in str(e).lower():
                            # Order no longer exists, remove from active orders
                            if order_id in self.active_orders:
                                del self.active_orders[order_id]
                        else:
                            self.logger.error(f"Error checking order {order_id}: {e}")
                            
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in order monitoring loop: {e}")
                await asyncio.sleep(30)
                
    async def _position_monitoring_loop(self):
        """Monitor positions and P&L"""
        while self.running:
            try:
                # Get current positions
                positions = await self.get_positions()
                
                # Calculate daily P&L
                total_unrealized_pl = sum(pos.unrealized_pl for pos in positions)
                
                # Check circuit breaker
                if total_unrealized_pl < -self.daily_loss_limit:
                    if not self.circuit_breaker_active:
                        self.circuit_breaker_active = True
                        self.logger.critical(f"Circuit breaker activated! Daily loss: {total_unrealized_pl}")
                        
                        # Cancel all active orders
                        for order_id in list(self.active_orders.keys()):
                            await self.cancel_order(order_id)
                            
                # Store position data
                for position in positions:
                    await self.storage.store_cache(
                        f"position:{position.symbol}",
                        position.dict(),
                        ttl=300
                    )
                    
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in position monitoring loop: {e}")
                await asyncio.sleep(60)
