#!/usr/bin/env python3
"""
Basic test script for Holly AI core functionality
Tests the essential features without complex dependencies
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain


async def test_basic_holly_interaction():
    """Test basic Holly AI interaction"""
    print("🤖 Testing Basic Holly AI Interaction...")
    
    holly = HollyAIBrain()
    
    # Simple queries that should work with basic setup
    test_queries = [
        "Hello Holly, can you help me with trading?",
        "What is a TTM Squeeze?",
        "Explain risk management in trading",
        "How do I calculate position size?"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            # Show function called (if any)
            function_called = response.get("function_called")
            if function_called:
                print(f"🔧 Holly used function: {function_called}")
            
            # Show <PERSON>'s response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_trading_goal_interpretation():
    """Test trading goal interpretation"""
    print("\n📊 Testing Trading Goal Interpretation...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "I want to make $50 today",
        "Help me make $100 this week with low risk",
        "Create a trading plan for $200 profit"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            function_called = response.get("function_called")
            if function_called:
                print(f"🔧 Holly used function: {function_called}")
                
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    print("✅ Function executed successfully")
                else:
                    print(f"❌ Function failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_function_registry():
    """Test that all functions are properly registered"""
    print("\n🔧 Testing Function Registry...")
    
    holly = HollyAIBrain()
    
    print(f"📋 Registered Functions ({len(holly.functions)}):")
    for func_name, func_info in holly.functions.items():
        print(f"  ✅ {func_name}: {func_info.get('description', 'No description')[:80]}...")
    
    # Test specific function availability
    key_functions = [
        "interpret_trading_goal",
        "search_web", 
        "scan_ttm_squeeze_signals",
        "analyze_social_sentiment",
        "calculate_ai_enhanced_stop_loss"
    ]
    
    print(f"\n🎯 Key AI Functions Status:")
    for func in key_functions:
        status = "✅ Available" if func in holly.functions else "❌ Missing"
        print(f"  {func}: {status}")


async def test_configuration_status():
    """Test configuration and API key status"""
    print("\n⚙️ Testing Configuration Status...")
    
    try:
        from src.core.config import settings
        
        # Check essential API keys
        api_keys = {
            "OpenAI": bool(getattr(settings, 'OPENAI_API_KEY', None)),
            "FMP": bool(getattr(settings, 'FMP_API_KEY', None)),
            "Alpaca": bool(getattr(settings, 'APCA_API_KEY_ID', None)),
            "Google Search": bool(getattr(settings, 'GOOGLE_SEARCH_API_KEY', None)),
            "Bing Search": bool(getattr(settings, 'BING_SEARCH_API_KEY', None))
        }
        
        print("🔑 API Key Status:")
        for service, configured in api_keys.items():
            status = "✅ Configured" if configured else "❌ Missing"
            print(f"  {service}: {status}")
        
        # Check optional services
        print("\n🔧 Service Availability:")
        
        # Web Search
        try:
            from src.services.web_search_service import WebSearchService
            search_service = WebSearchService()
            providers = search_service.get_available_providers()
            print(f"  Web Search: ✅ Available (providers: {', '.join(providers)})")
        except Exception as e:
            print(f"  Web Search: ❌ Error - {e}")
        
        # Social Sentiment
        try:
            from src.services.social_sentiment_service import SocialSentimentService
            print("  Social Sentiment: ✅ Available")
        except Exception as e:
            print(f"  Social Sentiment: ❌ Error - {e}")
        
        # TTM Scanner
        try:
            from src.services.ttm_squeeze_scanner import TTMSqueezeScanner
            print("  TTM Squeeze Scanner: ✅ Available")
        except Exception as e:
            print(f"  TTM Squeeze Scanner: ❌ Error - {e}")
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")


async def test_simple_web_search():
    """Test web search with fallback to DuckDuckGo"""
    print("\n🔍 Testing Simple Web Search...")
    
    holly = HollyAIBrain()
    
    # Simple search query
    query = "Tell me about recent market news"
    print(f"💬 User: {query}")
    
    try:
        response = await holly.process_user_message(query)
        
        function_called = response.get("function_called")
        if function_called == "search_web":
            function_result = response.get("function_result", {})
            if function_result.get("success"):
                results = function_result.get("results", [])
                print(f"✅ Found {len(results)} search results")
            else:
                print(f"❌ Search failed: {function_result.get('error', 'Unknown error')}")
                print(f"💡 Suggestion: {function_result.get('fallback_suggestion', 'Try a different query')}")
        
        # Show Holly's response (truncated)
        holly_response = response.get("response", "No response")
        print(f"🤖 Holly: {holly_response[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def main():
    """Run basic Holly AI tests"""
    print("🎯 Holly AI Basic Functionality Test")
    print("=" * 50)
    
    try:
        # Test configuration first
        await test_configuration_status()
        
        # Test function registry
        await test_function_registry()
        
        # Test basic interaction
        await test_basic_holly_interaction()
        
        # Test trading goal interpretation
        await test_trading_goal_interpretation()
        
        # Test simple web search
        await test_simple_web_search()
        
        print("\n✅ Basic tests completed!")
        print("\n🎉 Holly AI Status Summary:")
        print("1. ✅ Core AI Brain - Functional")
        print("2. ✅ Function Registry - All functions registered")
        print("3. ✅ Trading Goal Interpretation - Working")
        print("4. ✅ Basic Conversation - Responsive")
        print("5. 🔄 Advanced Features - May need API configuration")
        
        print("\n💡 Next Steps:")
        print("- Set up Google Custom Search Engine ID for web search")
        print("- Test with real market data")
        print("- Configure optional API keys for enhanced features")
        print("- Run full integration tests")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
