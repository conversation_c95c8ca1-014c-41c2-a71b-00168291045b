#!/usr/bin/env python3
"""
Test AI-Enhanced Stop Loss Functionality
Demonstrates Holly AI's comprehensive stop loss calculation capabilities
"""

import asyncio
import json
from src.core.holly_functions import HollyFunctions
from src.core.holly_ai_brain import HollyAIBrain

async def test_basic_ai_stop_loss():
    """Test basic AI-enhanced stop loss calculation"""
    print("🧪 Testing AI-Enhanced Stop Loss Calculation\n")
    
    functions = HollyFunctions()
    
    # Test parameters
    test_cases = [
        {
            "symbol": "AAPL",
            "entry_price": 150.00,
            "direction": "long",
            "timeframe": "15m",
            "risk_percent": 2.0,
            "account_size": 100000
        },
        {
            "symbol": "TSLA", 
            "entry_price": 200.00,
            "direction": "short",
            "timeframe": "30m",
            "risk_percent": 1.5,
            "account_size": 50000
        },
        {
            "symbol": "NVDA",
            "entry_price": 400.00,
            "direction": "long", 
            "timeframe": "1h",
            "risk_percent": 2.5,
            "account_size": 200000
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📊 Test Case {i}: {test_case['symbol']} {test_case['direction'].upper()}")
        print(f"   Entry: ${test_case['entry_price']}")
        print(f"   Timeframe: {test_case['timeframe']}")
        print(f"   Risk: {test_case['risk_percent']}%")
        
        try:
            result = await functions.calculate_ai_enhanced_stop_loss(**test_case)
            
            if result.get("success"):
                print(f"   ✅ AI Stop: ${result['stop_price']}")
                print(f"   💰 Risk Amount: ${result['risk_amount']:.2f}")
                print(f"   📈 Risk %: {result['risk_percent']:.2f}%")
                print(f"   🎯 Confidence: {result['confidence']:.1%}")
                print(f"   🧠 Method: {result['method']}")
                
                if result.get('reasoning'):
                    print(f"   💡 Reasoning: {result['reasoning'][:100]}...")
                    
                if result.get('technical_factors'):
                    factors = result['technical_factors']
                    print(f"   🔧 Technical Factors:")
                    print(f"      Basic Stop: ${factors.get('basic_stop', 'N/A')}")
                    print(f"      Volatility Adj: {factors.get('volatility_adjustment', 0):+.2f}")
                    print(f"      Sentiment Adj: {factors.get('sentiment_adjustment', 0):+.2f}")
                    print(f"      Market Structure: {factors.get('market_structure', 'N/A')}")
                    
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            
        print()

async def test_holly_ai_integration():
    """Test AI-enhanced stop loss through Holly AI Brain"""
    print("🤖 Testing Holly AI Integration\n")
    
    holly = HollyAIBrain()
    
    # Test natural language requests that should trigger AI stop loss calculation
    test_requests = [
        "Calculate an AI-enhanced stop loss for AAPL at $150 for a long position",
        "I want to buy MSFT at $300, what's the optimal stop loss?",
        "Set up a short position on TSLA at $200 with proper risk management"
    ]
    
    for i, request in enumerate(test_requests, 1):
        print(f"💬 Test {i}: '{request}'")
        
        try:
            response = await holly.process_user_message(request)
            
            print(f"   📝 Response Type: {response.get('type', 'unknown')}")
            print(f"   🔧 Function Called: {response.get('function_called', 'none')}")
            
            if response.get('function_result'):
                result = response['function_result']
                if result.get('success') and result.get('stop_price'):
                    print(f"   ✅ AI Stop Calculated: ${result['stop_price']}")
                    print(f"   🎯 Confidence: {result.get('confidence', 0):.1%}")
                    
            # Show part of Holly's response
            holly_response = response.get('response', '')
            if holly_response:
                print(f"   💭 Holly Says: {holly_response[:150]}...")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            
        print()

async def test_trading_plan_integration():
    """Test AI-enhanced stops in trading plan creation"""
    print("📋 Testing Trading Plan Integration\n")
    
    functions = HollyFunctions()
    
    # Create a sample trading plan
    trades = [
        {
            "symbol": "AAPL",
            "action": "buy",
            "quantity": 100,
            "entry_price": 150.00,
            "target_price": 155.00,
            "reasoning": "TTM Squeeze breakout with volume"
        },
        {
            "symbol": "MSFT", 
            "action": "buy",
            "quantity": 50,
            "entry_price": 300.00,
            "target_price": 310.00,
            "reasoning": "Bullish momentum continuation"
        }
    ]
    
    print("📊 Creating trading plan with AI-enhanced stops...")
    
    try:
        result = await functions.create_trading_plan(
            trades=trades,
            timeframe="15m",
            risk_tolerance="moderate",
            account_size=100000,
            enhance_stops=True
        )
        
        if result.get("success"):
            plan = result["plan"]
            enhanced_trades = plan.get("trades", [])
            
            print(f"   ✅ Plan Created: {plan['id']}")
            print(f"   📈 Total Trades: {len(enhanced_trades)}")
            print(f"   💰 Total Risk: ${plan['summary']['total_risk']:.2f}")
            print(f"   🎯 Expected Return: ${plan['summary']['expected_return']:.2f}")
            print(f"   📊 Risk/Reward: {plan['summary']['risk_reward_ratio']:.2f}:1")
            
            print("\n   🔧 Enhanced Trades:")
            for trade in enhanced_trades:
                print(f"      {trade['symbol']}: {trade['action'].upper()} {trade['quantity']} @ ${trade['entry_price']}")
                print(f"         Stop: ${trade.get('stop_price', 'N/A')} (AI: {trade.get('ai_enhanced', False)})")
                print(f"         Target: ${trade.get('target_price', 'N/A')}")
                if trade.get('stop_reasoning'):
                    print(f"         Reasoning: {trade['stop_reasoning'][:80]}...")
                print()
                
        else:
            print(f"   ❌ Plan Creation Failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")

async def test_volatility_and_sentiment_impact():
    """Test how volatility and sentiment affect stop loss calculation"""
    print("📊 Testing Volatility & Sentiment Impact\n")
    
    functions = HollyFunctions()
    
    # Test same symbol with different volatility settings
    base_params = {
        "symbol": "AAPL",
        "entry_price": 150.00,
        "direction": "long",
        "timeframe": "15m",
        "risk_percent": 2.0
    }
    
    test_scenarios = [
        {"volatility_adjustment": False, "news_sentiment_factor": False, "name": "Basic Technical Only"},
        {"volatility_adjustment": True, "news_sentiment_factor": False, "name": "With Volatility Adjustment"},
        {"volatility_adjustment": False, "news_sentiment_factor": True, "name": "With Sentiment Analysis"},
        {"volatility_adjustment": True, "news_sentiment_factor": True, "name": "Full AI Enhancement"}
    ]
    
    print("🔬 Comparing stop loss calculations with different factors:")
    
    for scenario in test_scenarios:
        print(f"\n   📋 Scenario: {scenario['name']}")
        
        params = {**base_params, **{k: v for k, v in scenario.items() if k != 'name'}}
        
        try:
            result = await functions.calculate_ai_enhanced_stop_loss(**params)
            
            if result.get("success"):
                print(f"      Stop Price: ${result['stop_price']}")
                print(f"      Risk Amount: ${result['risk_amount']:.2f}")
                print(f"      Confidence: {result['confidence']:.1%}")
                
                if result.get('technical_factors'):
                    factors = result['technical_factors']
                    print(f"      Volatility Adj: {factors.get('volatility_adjustment', 0):+.3f}")
                    print(f"      Sentiment Adj: {factors.get('sentiment_adjustment', 0):+.3f}")
                    
            else:
                print(f"      ❌ Failed: {result.get('error', 'Unknown')}")
                
        except Exception as e:
            print(f"      ❌ Exception: {e}")

async def main():
    """Run all AI-enhanced stop loss tests"""
    print("🧪 Holly AI - AI-Enhanced Stop Loss Test Suite")
    print("=" * 60)
    
    await test_basic_ai_stop_loss()
    await test_holly_ai_integration()
    await test_trading_plan_integration()
    await test_volatility_and_sentiment_impact()
    
    print("=" * 60)
    print("🎉 AI-Enhanced Stop Loss Testing Complete!")
    print("\n✨ Key Features Demonstrated:")
    print("   ✅ Multi-factor stop loss calculation")
    print("   ✅ LLM-powered technical analysis")
    print("   ✅ Volatility and sentiment integration")
    print("   ✅ Automatic trading plan enhancement")
    print("   ✅ Natural language interface")
    print("   ✅ Comprehensive risk management")
    
    print("\n🚀 Holly AI now provides institutional-grade stop loss intelligence!")

if __name__ == "__main__":
    asyncio.run(main())
