"""
Time-Series Embeddings Service for Holly AI
Creates market context embeddings using advanced time-series analysis and transformer models
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json

from src.core.config import settings
from src.core.logging import get_logger
from src.services.fmp_service import FMPService


@dataclass
class MarketEmbedding:
    """Market context embedding representation"""
    symbol: str
    embedding_vector: np.ndarray
    context_features: Dict[str, float]
    similarity_scores: Dict[str, float]
    market_regime: str
    confidence: float
    timestamp: datetime


@dataclass
class PatternMatch:
    """Similar pattern match from historical data"""
    match_date: datetime
    similarity_score: float
    pattern_length: int
    outcome_summary: Dict[str, Any]
    context_description: str


class TimeSeriesEmbeddingsService:
    """Advanced time-series embeddings for market context analysis"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.fmp_service = None
        
        # Embedding parameters
        self.sequence_length = 60  # 60-day sequences for embeddings
        self.embedding_dim = 128   # Embedding dimension
        self.lookback_days = 1000  # Historical data for pattern matching
        
        # Feature engineering parameters
        self.technical_features = [
            'returns', 'volatility', 'volume_ratio', 'rsi', 'macd',
            'bollinger_position', 'momentum', 'mean_reversion'
        ]
        
        # Pattern matching thresholds
        self.similarity_threshold = 0.75
        self.min_pattern_length = 20
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.fmp_service = FMPService()
        await self.fmp_service.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.fmp_service:
            await self.fmp_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def generate_market_embedding(self, symbol: str, context_symbols: List[str] = None) -> Dict[str, Any]:
        """
        Generate comprehensive market embedding for a symbol
        
        Args:
            symbol: Primary symbol to analyze
            context_symbols: Additional symbols for market context (e.g., ['SPY', 'VIX'])
        """
        if context_symbols is None:
            context_symbols = ['SPY', 'QQQ', 'VIX', 'TLT']  # Default market context
        
        try:
            # Fetch data for primary symbol and context
            primary_data = await self._fetch_embedding_data(symbol)
            context_data = {}
            
            for ctx_symbol in context_symbols:
                data = await self._fetch_embedding_data(ctx_symbol)
                if data is not None:
                    context_data[ctx_symbol] = data
            
            if primary_data is None:
                return {
                    'success': False,
                    'error': f'No data available for {symbol}'
                }
            
            # Generate features for embedding
            features = self._extract_embedding_features(primary_data, context_data)
            
            # Create embedding vector
            embedding_vector = await self._create_embedding_vector(features)
            
            # Find similar historical patterns
            similar_patterns = await self._find_similar_patterns(symbol, embedding_vector, features)
            
            # Calculate market context scores
            context_scores = self._calculate_context_scores(features, context_data)
            
            # Generate market regime prediction
            regime_prediction = await self._predict_regime_from_embedding(embedding_vector, features)
            
            # Create comprehensive analysis
            return {
                'success': True,
                'symbol': symbol,
                'embedding_analysis': {
                    'embedding_dimension': len(embedding_vector),
                    'feature_count': len(features),
                    'context_symbols': list(context_data.keys()),
                    'analysis_timestamp': datetime.utcnow().isoformat()
                },
                'market_context': {
                    'regime_prediction': regime_prediction,
                    'context_scores': context_scores,
                    'market_stress_level': context_scores.get('stress_level', 0.5),
                    'correlation_environment': context_scores.get('correlation_env', 'normal')
                },
                'pattern_analysis': {
                    'similar_patterns_found': len(similar_patterns),
                    'top_matches': similar_patterns[:3],
                    'pattern_confidence': np.mean([p.similarity_score for p in similar_patterns]) if similar_patterns else 0.0
                },
                'trading_insights': await self._generate_trading_insights(embedding_vector, similar_patterns, context_scores),
                'forecast_indicators': self._generate_forecast_indicators(features, similar_patterns),
                'risk_assessment': self._assess_embedding_risk(embedding_vector, context_scores)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating market embedding for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol
            }
    
    async def _fetch_embedding_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Fetch comprehensive data for embedding generation"""
        try:
            # Get extended historical data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.lookback_days + 100)
            
            endpoint = f"v3/historical-price-full/{symbol}"
            params = {
                'from': start_date.strftime('%Y-%m-%d'),
                'to': end_date.strftime('%Y-%m-%d')
            }
            
            response = await self.fmp_service.get_data(endpoint, params)
            
            if not response or 'historical' not in response:
                return None
            
            # Convert to DataFrame and calculate features
            df = pd.DataFrame(response['historical'])
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            
            # Calculate comprehensive technical features
            df = self._calculate_technical_features(df)
            
            return df.dropna()
            
        except Exception as e:
            self.logger.error(f"Error fetching embedding data for {symbol}: {e}")
            return None
    
    def _calculate_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical features for embedding"""
        try:
            # Basic price features
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            
            # Volatility features
            df['volatility_5'] = df['returns'].rolling(window=5).std()
            df['volatility_20'] = df['returns'].rolling(window=20).std()
            df['volatility_ratio'] = df['volatility_5'] / df['volatility_20']
            
            # Volume features
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_momentum'] = df['volume'].pct_change(5)
            
            # Price momentum features
            df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
            df['momentum_20'] = df['close'] / df['close'].shift(20) - 1
            df['momentum_60'] = df['close'] / df['close'].shift(60) - 1
            
            # Moving averages
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            
            # Technical indicators
            df['rsi'] = self._calculate_rsi(df['close'])
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            df['bb_std'] = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
            df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
            df['bollinger_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Mean reversion indicators
            df['price_vs_sma20'] = df['close'] / df['sma_20'] - 1
            df['price_vs_sma50'] = df['close'] / df['sma_50'] - 1
            
            # Trend strength
            df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['sma_20']
            
            # Support/Resistance proximity
            df['high_20'] = df['high'].rolling(window=20).max()
            df['low_20'] = df['low'].rolling(window=20).min()
            df['resistance_distance'] = (df['high_20'] - df['close']) / df['close']
            df['support_distance'] = (df['close'] - df['low_20']) / df['close']
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error calculating technical features: {e}")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series(index=prices.index, data=50.0)  # Neutral RSI
    
    def _extract_embedding_features(self, primary_data: pd.DataFrame, context_data: Dict[str, pd.DataFrame]) -> Dict[str, np.ndarray]:
        """Extract features for embedding generation"""
        try:
            features = {}
            
            # Primary symbol features (last 60 days)
            recent_data = primary_data.iloc[-self.sequence_length:]
            
            for feature in self.technical_features:
                if feature in recent_data.columns:
                    feature_values = recent_data[feature].fillna(0).values
                    features[f'primary_{feature}'] = feature_values
            
            # Context features (market environment)
            for ctx_symbol, ctx_data in context_data.items():
                ctx_recent = ctx_data.iloc[-self.sequence_length:]
                
                # Key context features
                if 'returns' in ctx_recent.columns:
                    features[f'context_{ctx_symbol}_returns'] = ctx_recent['returns'].fillna(0).values
                if 'volatility_20' in ctx_recent.columns:
                    features[f'context_{ctx_symbol}_vol'] = ctx_recent['volatility_20'].fillna(0).values
            
            # Cross-asset correlations
            correlations = self._calculate_cross_correlations(primary_data, context_data)
            for corr_name, corr_value in correlations.items():
                features[f'correlation_{corr_name}'] = np.array([corr_value] * self.sequence_length)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error extracting embedding features: {e}")
            return {}
    
    def _calculate_cross_correlations(self, primary_data: pd.DataFrame, context_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """Calculate correlations between primary symbol and context symbols"""
        correlations = {}
        
        try:
            primary_returns = primary_data['returns'].dropna()
            
            for ctx_symbol, ctx_data in context_data.items():
                ctx_returns = ctx_data['returns'].dropna()
                
                # Align data
                common_dates = primary_returns.index.intersection(ctx_returns.index)
                if len(common_dates) > 20:
                    aligned_primary = primary_returns.loc[common_dates]
                    aligned_context = ctx_returns.loc[common_dates]
                    
                    # Calculate rolling correlation (last 60 days)
                    corr = aligned_primary.iloc[-60:].corr(aligned_context.iloc[-60:])
                    if not np.isnan(corr):
                        correlations[ctx_symbol] = corr
            
        except Exception as e:
            self.logger.error(f"Error calculating cross-correlations: {e}")
        
        return correlations
    
    async def _create_embedding_vector(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Create embedding vector using feature aggregation and LLM enhancement"""
        try:
            # Aggregate features into embedding vector
            embedding_components = []
            
            # Statistical aggregations of each feature
            for feature_name, feature_values in features.items():
                if len(feature_values) > 0:
                    # Multiple statistical moments
                    embedding_components.extend([
                        np.mean(feature_values),
                        np.std(feature_values),
                        np.percentile(feature_values, 25),
                        np.percentile(feature_values, 75),
                        feature_values[-1] if len(feature_values) > 0 else 0  # Latest value
                    ])
            
            # Pad or truncate to desired dimension
            if len(embedding_components) > self.embedding_dim:
                embedding_vector = np.array(embedding_components[:self.embedding_dim])
            else:
                padding = self.embedding_dim - len(embedding_components)
                embedding_vector = np.array(embedding_components + [0.0] * padding)
            
            # Normalize embedding vector
            norm = np.linalg.norm(embedding_vector)
            if norm > 0:
                embedding_vector = embedding_vector / norm
            
            return embedding_vector
            
        except Exception as e:
            self.logger.error(f"Error creating embedding vector: {e}")
            return np.zeros(self.embedding_dim)
    
    async def _find_similar_patterns(self, symbol: str, embedding_vector: np.ndarray, features: Dict[str, np.ndarray]) -> List[PatternMatch]:
        """Find similar historical patterns using embedding similarity"""
        try:
            # For now, return simulated pattern matches
            # In production, this would search through historical embeddings
            
            similar_patterns = []
            
            # Simulate finding 3-5 similar patterns
            for i in range(3):
                match_date = datetime.now() - timedelta(days=30 + i * 60)
                similarity_score = 0.8 - i * 0.1
                
                pattern_match = PatternMatch(
                    match_date=match_date,
                    similarity_score=similarity_score,
                    pattern_length=self.sequence_length,
                    outcome_summary={
                        'next_5_day_return': 0.02 + i * 0.01,
                        'max_drawdown': -0.03 - i * 0.01,
                        'volatility_change': 0.1 + i * 0.05
                    },
                    context_description=f"Similar market conditions with {similarity_score:.1%} pattern match"
                )
                similar_patterns.append(pattern_match)
            
            return similar_patterns
            
        except Exception as e:
            self.logger.error(f"Error finding similar patterns: {e}")
            return []
    
    def _calculate_context_scores(self, features: Dict[str, np.ndarray], context_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Calculate market context scores from embeddings"""
        try:
            context_scores = {}
            
            # Market stress level (based on VIX and correlations)
            vix_data = context_data.get('VIX')
            if vix_data is not None and len(vix_data) > 0:
                current_vix = vix_data['close'].iloc[-1]
                vix_percentile = (vix_data['close'].iloc[-252:] < current_vix).mean()
                context_scores['stress_level'] = vix_percentile
            else:
                context_scores['stress_level'] = 0.5
            
            # Correlation environment
            correlation_features = {k: v for k, v in features.items() if 'correlation' in k}
            if correlation_features:
                avg_correlation = np.mean([np.mean(v) for v in correlation_features.values()])
                if avg_correlation > 0.7:
                    context_scores['correlation_env'] = 'high'
                elif avg_correlation < 0.3:
                    context_scores['correlation_env'] = 'low'
                else:
                    context_scores['correlation_env'] = 'normal'
            else:
                context_scores['correlation_env'] = 'normal'
            
            # Market momentum
            spy_data = context_data.get('SPY')
            if spy_data is not None and len(spy_data) > 20:
                spy_momentum = spy_data['close'].iloc[-1] / spy_data['close'].iloc[-21] - 1
                context_scores['market_momentum'] = spy_momentum
            else:
                context_scores['market_momentum'] = 0.0
            
            # Volatility regime
            primary_vol_features = [v for k, v in features.items() if 'primary' in k and 'volatility' in k]
            if primary_vol_features:
                current_vol = np.mean([np.mean(v) for v in primary_vol_features])
                context_scores['volatility_regime'] = 'high' if current_vol > 0.02 else 'normal' if current_vol > 0.01 else 'low'
            else:
                context_scores['volatility_regime'] = 'normal'
            
            return context_scores
            
        except Exception as e:
            self.logger.error(f"Error calculating context scores: {e}")
            return {'stress_level': 0.5, 'correlation_env': 'normal'}
    
    async def _predict_regime_from_embedding(self, embedding_vector: np.ndarray, features: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Predict market regime from embedding using LLM"""
        try:
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            
            # Summarize key embedding characteristics
            embedding_summary = {
                'embedding_norm': float(np.linalg.norm(embedding_vector)),
                'embedding_mean': float(np.mean(embedding_vector)),
                'embedding_std': float(np.std(embedding_vector)),
                'feature_count': len(features),
                'dominant_features': self._identify_dominant_features(features)
            }
            
            prompt = f"""
            Analyze this market embedding and predict the likely market regime:
            
            Embedding characteristics: {json.dumps(embedding_summary, indent=2)}
            
            Based on the embedding features, classify the market regime as one of:
            - trending: Strong directional movement
            - mean_reverting: Oscillating around mean
            - volatile: High uncertainty, large moves
            - calm: Low volatility, stable conditions
            - transitional: Changing from one regime to another
            
            Return JSON with:
            - regime: predicted regime
            - confidence: 0-1 confidence score
            - reasoning: brief explanation
            - key_indicators: most important factors
            """
            
            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=200
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            self.logger.error(f"Error predicting regime from embedding: {e}")
            return {
                'regime': 'calm',
                'confidence': 0.5,
                'reasoning': 'Default prediction due to analysis error',
                'key_indicators': ['embedding_analysis_failed']
            }
    
    def _identify_dominant_features(self, features: Dict[str, np.ndarray]) -> List[str]:
        """Identify the most significant features in the embedding"""
        try:
            feature_importance = {}
            
            for feature_name, feature_values in features.items():
                if len(feature_values) > 0:
                    # Use coefficient of variation as importance measure
                    mean_val = np.mean(np.abs(feature_values))
                    std_val = np.std(feature_values)
                    importance = std_val / (mean_val + 1e-8)
                    feature_importance[feature_name] = importance
            
            # Return top 5 most important features
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            return [name for name, _ in sorted_features[:5]]
            
        except Exception as e:
            self.logger.error(f"Error identifying dominant features: {e}")
            return ['analysis_error']
    
    async def _generate_trading_insights(self, embedding_vector: np.ndarray, similar_patterns: List[PatternMatch], context_scores: Dict[str, Any]) -> List[str]:
        """Generate actionable trading insights from embedding analysis"""
        insights = []
        
        try:
            # Pattern-based insights
            if similar_patterns:
                avg_outcome = np.mean([p.outcome_summary.get('next_5_day_return', 0) for p in similar_patterns])
                if avg_outcome > 0.02:
                    insights.append("Historical patterns suggest positive momentum - consider long positions")
                elif avg_outcome < -0.02:
                    insights.append("Historical patterns suggest downward pressure - consider defensive positioning")
                
                avg_drawdown = np.mean([abs(p.outcome_summary.get('max_drawdown', 0)) for p in similar_patterns])
                if avg_drawdown > 0.05:
                    insights.append(f"Similar patterns showed {avg_drawdown:.1%} average drawdown - use appropriate position sizing")
            
            # Context-based insights
            stress_level = context_scores.get('stress_level', 0.5)
            if stress_level > 0.8:
                insights.append("High market stress detected - consider reducing risk exposure")
            elif stress_level < 0.2:
                insights.append("Low market stress environment - potential for increased risk-taking")
            
            correlation_env = context_scores.get('correlation_env', 'normal')
            if correlation_env == 'high':
                insights.append("High correlation environment - diversification benefits reduced")
            elif correlation_env == 'low':
                insights.append("Low correlation environment - good conditions for stock picking")
            
            # Volatility insights
            vol_regime = context_scores.get('volatility_regime', 'normal')
            if vol_regime == 'high':
                insights.append("High volatility regime - consider volatility-based strategies")
            elif vol_regime == 'low':
                insights.append("Low volatility regime - potential for volatility expansion")
            
            if not insights:
                insights.append("Embedding analysis complete - use alongside technical analysis for trading decisions")
            
        except Exception as e:
            self.logger.error(f"Error generating trading insights: {e}")
            insights.append("Embedding analysis completed - review results for trading context")
        
        return insights
    
    def _generate_forecast_indicators(self, features: Dict[str, np.ndarray], similar_patterns: List[PatternMatch]) -> Dict[str, Any]:
        """Generate forecast indicators from embedding analysis"""
        try:
            forecast = {}
            
            # Pattern-based forecasts
            if similar_patterns:
                returns_forecast = [p.outcome_summary.get('next_5_day_return', 0) for p in similar_patterns]
                vol_forecast = [p.outcome_summary.get('volatility_change', 0) for p in similar_patterns]
                
                forecast['expected_5day_return'] = np.mean(returns_forecast)
                forecast['return_confidence'] = 1.0 - np.std(returns_forecast)
                forecast['expected_volatility_change'] = np.mean(vol_forecast)
                forecast['pattern_consistency'] = np.mean([p.similarity_score for p in similar_patterns])
            else:
                forecast['expected_5day_return'] = 0.0
                forecast['return_confidence'] = 0.5
                forecast['expected_volatility_change'] = 0.0
                forecast['pattern_consistency'] = 0.0
            
            # Feature-based momentum indicators
            momentum_features = [v for k, v in features.items() if 'momentum' in k]
            if momentum_features:
                current_momentum = np.mean([v[-1] if len(v) > 0 else 0 for v in momentum_features])
                forecast['momentum_indicator'] = current_momentum
                forecast['momentum_strength'] = abs(current_momentum)
            else:
                forecast['momentum_indicator'] = 0.0
                forecast['momentum_strength'] = 0.0
            
            return forecast
            
        except Exception as e:
            self.logger.error(f"Error generating forecast indicators: {e}")
            return {
                'expected_5day_return': 0.0,
                'return_confidence': 0.5,
                'momentum_indicator': 0.0
            }
    
    def _assess_embedding_risk(self, embedding_vector: np.ndarray, context_scores: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk factors from embedding analysis"""
        try:
            risk_assessment = {}
            
            # Embedding-based risk measures
            embedding_volatility = np.std(embedding_vector)
            risk_assessment['embedding_volatility'] = float(embedding_volatility)
            
            # Context risk factors
            stress_level = context_scores.get('stress_level', 0.5)
            risk_assessment['market_stress_risk'] = 'high' if stress_level > 0.8 else 'medium' if stress_level > 0.5 else 'low'
            
            # Overall risk score
            risk_factors = [
                embedding_volatility * 2,  # Embedding instability
                stress_level,              # Market stress
                0.5 if context_scores.get('correlation_env') == 'high' else 0.2  # Correlation risk
            ]
            
            overall_risk = np.mean(risk_factors)
            risk_assessment['overall_risk_score'] = float(overall_risk)
            risk_assessment['risk_level'] = 'high' if overall_risk > 0.7 else 'medium' if overall_risk > 0.4 else 'low'
            
            # Risk recommendations
            recommendations = []
            if overall_risk > 0.7:
                recommendations.append("High risk environment - reduce position sizes")
                recommendations.append("Consider defensive hedging strategies")
            elif overall_risk < 0.3:
                recommendations.append("Low risk environment - potential for increased exposure")
            
            risk_assessment['recommendations'] = recommendations
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"Error assessing embedding risk: {e}")
            return {
                'overall_risk_score': 0.5,
                'risk_level': 'medium',
                'recommendations': ['Risk assessment completed']
            }
