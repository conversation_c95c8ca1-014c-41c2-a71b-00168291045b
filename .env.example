# Alpaca API Configuration (Paper Trading)
APCA_API_BASE_URL=https://paper-api.alpaca.markets
APCA_API_KEY_ID=PKI0KNC8HXZURYRA4OMC
APCA_API_SECRET_KEY=7ydtObOUVC22xP2IJbEhetmKrvec7N9owdcor0hn

# Financial Modeling Prep API
FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7
FMP_BASE_URL=https://financialmodelingprep.com/api

# OpenAI API
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.2

# Web Search APIs (Optional - for enhanced information gathering)
# Choose one or more providers for Holly to search the web when internal data is insufficient

# Google Custom Search (Recommended - Best results)
# Get API key: https://developers.google.com/custom-search/v1/introduction
# Create search engine: https://cse.google.com/cse/
GOOGLE_SEARCH_API_KEY=AIzaSyAq7wJ7gO60cVtyzcFIp6kIgwPQaHsIny0
GOOGLE_SEARCH_ENGINE_ID=placeholder_need_to_create_custom_search_engine

# Bing Search API (Alternative)
# Get API key: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
BING_SEARCH_API_KEY=your_bing_search_api_key

# Note: DuckDuckGo is available as fallback but provides limited results

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# InfluxDB Configuration
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=holly-ai
INFLUXDB_BUCKET=market-data

# Application Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=True

# Trading Configuration
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10
PAPER_TRADING=True

# Monitoring
PROMETHEUS_PORT=8000
