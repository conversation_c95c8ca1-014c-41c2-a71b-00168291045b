#!/usr/bin/env python3
"""
Test script for Holly AI's advanced AI features
Tests regime detection, time-series embeddings, and integrated workflows
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain


async def test_market_regime_detection():
    """Test market regime detection functionality"""
    print("🌊 Testing Market Regime Detection...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "What's the current market regime?",
        "Detect the market regime for SPY, QQQ, and VIX",
        "Analyze market conditions and tell me what regime we're in"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "detect_market_regime":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    regime_analysis = function_result.get("market_regime_analysis", {})
                    print(f"✅ Market Regime Analysis:")
                    print(f"   Current Regime: {regime_analysis.get('current_regime', 'unknown')}")
                    print(f"   Confidence: {regime_analysis.get('confidence', 0):.2f}")
                    print(f"   Duration: {regime_analysis.get('regime_duration_days', 0)} days")
                    print(f"   Change Probability: {regime_analysis.get('regime_change_probability', 0):.2f}")
                    
                    strategies = function_result.get("strategy_recommendations", [])
                    if strategies:
                        print(f"   Recommended Strategies: {[s.get('strategy') for s in strategies[:3]]}")
                else:
                    print(f"❌ Regime detection failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_time_series_embeddings():
    """Test time-series embeddings functionality"""
    print("\n🧠 Testing Time-Series Embeddings...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "Generate market embeddings for AAPL",
        "Analyze TSLA using time-series embeddings with SPY and VIX context",
        "Create market context embeddings for NVDA"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "generate_market_embedding":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    embedding_summary = function_result.get("embedding_summary", {})
                    market_context = function_result.get("market_context", {})
                    pattern_analysis = function_result.get("pattern_analysis", {})
                    
                    print(f"✅ Market Embedding Analysis:")
                    print(f"   Symbol: {function_result.get('symbol', 'unknown')}")
                    print(f"   Embedding Dimension: {embedding_summary.get('embedding_dimension', 0)}")
                    print(f"   Feature Count: {embedding_summary.get('feature_count', 0)}")
                    print(f"   Context Symbols: {embedding_summary.get('context_symbols', [])}")
                    
                    if market_context:
                        regime_pred = market_context.get('regime_prediction', {})
                        print(f"   Predicted Regime: {regime_pred.get('regime', 'unknown')}")
                        print(f"   Market Stress: {market_context.get('market_stress_level', 0):.2f}")
                    
                    if pattern_analysis:
                        print(f"   Similar Patterns Found: {pattern_analysis.get('similar_patterns_found', 0)}")
                        print(f"   Pattern Confidence: {pattern_analysis.get('pattern_confidence', 0):.2f}")
                else:
                    print(f"❌ Embedding generation failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_integrated_advanced_workflow():
    """Test integrated workflow with all advanced AI features"""
    print("\n🚀 Testing Integrated Advanced AI Workflow...")
    
    holly = HollyAIBrain()
    
    # Complex query that should trigger multiple advanced functions
    complex_query = """
    I want to make $200 today with AAPL. First, analyze the current market regime, 
    then generate market embeddings for AAPL, check social sentiment, scan for TTM 
    Squeeze signals, and search for any recent news. Create a comprehensive trading 
    plan that considers all these AI insights.
    """
    
    print(f"💬 User: {complex_query}")
    
    try:
        response = await holly.process_user_message(complex_query)
        
        function_called = response.get("function_called")
        print(f"🔧 Holly used function: {function_called}")
        
        if function_called:
            function_result = response.get("function_result", {})
            if function_result.get("success"):
                print("✅ Function executed successfully")
                
                # Show key insights based on function type
                if function_called == "detect_market_regime":
                    regime = function_result.get("market_regime_analysis", {}).get("current_regime")
                    print(f"📊 Market Regime: {regime}")
                elif function_called == "generate_market_embedding":
                    symbol = function_result.get("symbol")
                    patterns = function_result.get("pattern_analysis", {}).get("similar_patterns_found", 0)
                    print(f"🧠 Embedding Analysis for {symbol}: {patterns} similar patterns found")
                elif function_called == "analyze_social_sentiment":
                    sentiment = function_result.get("analysis_summary", {}).get("overall_sentiment")
                    print(f"📱 Social Sentiment: {sentiment}")
                elif function_called == "scan_ttm_squeeze_signals":
                    signals = function_result.get("signals_found", 0)
                    print(f"📈 TTM Squeeze Signals: {signals} found")
                elif function_called == "search_web":
                    results = function_result.get("total_results", 0)
                    print(f"🔍 Web Search: {results} results found")
            else:
                print(f"❌ Function failed: {function_result.get('error', 'Unknown error')}")
        
        # Show Holly's full response
        holly_response = response.get("response", "No response")
        print(f"\n🤖 Holly's Response:")
        print(holly_response)
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def test_regime_based_strategy_adaptation():
    """Test how Holly adapts strategies based on regime detection"""
    print("\n⚙️ Testing Regime-Based Strategy Adaptation...")
    
    holly = HollyAIBrain()
    
    test_scenarios = [
        "The market is in a trending regime - what strategies should I use?",
        "If we're in a mean-reverting regime, how should I trade AAPL?",
        "Market regime shows high volatility - adjust my trading plan accordingly"
    ]
    
    for scenario in test_scenarios:
        print(f"\n💬 User: {scenario}")
        
        try:
            response = await holly.process_user_message(scenario)
            
            function_called = response.get("function_called")
            if function_called:
                print(f"🔧 Holly used function: {function_called}")
                
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    # Extract strategy recommendations
                    if function_called == "detect_market_regime":
                        strategies = function_result.get("strategy_recommendations", [])
                        implications = function_result.get("trading_implications", [])
                        
                        print(f"✅ Strategy Recommendations:")
                        for strategy in strategies[:3]:
                            print(f"   - {strategy.get('strategy', 'unknown')}: {strategy.get('details', 'No details')}")
                        
                        if implications:
                            print(f"📋 Trading Implications:")
                            for implication in implications[:2]:
                                print(f"   - {implication}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:150]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_embedding_pattern_matching():
    """Test pattern matching capabilities of embeddings"""
    print("\n🔍 Testing Embedding Pattern Matching...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "Find similar historical patterns for TSLA using embeddings",
        "What do the embeddings tell us about NVDA's current pattern?",
        "Use time-series analysis to predict AAPL's next move"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "generate_market_embedding":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    pattern_analysis = function_result.get("pattern_analysis", {})
                    forecast = function_result.get("forecast_indicators", {})
                    risk_assessment = function_result.get("risk_assessment", {})
                    
                    print(f"✅ Pattern Analysis:")
                    print(f"   Similar Patterns: {pattern_analysis.get('similar_patterns_found', 0)}")
                    print(f"   Pattern Confidence: {pattern_analysis.get('pattern_confidence', 0):.2f}")
                    
                    if forecast:
                        expected_return = forecast.get('expected_5day_return', 0)
                        momentum = forecast.get('momentum_indicator', 0)
                        print(f"   Expected 5-day Return: {expected_return:.2%}")
                        print(f"   Momentum Indicator: {momentum:.3f}")
                    
                    if risk_assessment:
                        risk_level = risk_assessment.get('risk_level', 'unknown')
                        risk_score = risk_assessment.get('overall_risk_score', 0)
                        print(f"   Risk Level: {risk_level} (score: {risk_score:.2f})")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:150]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def main():
    """Run all advanced AI feature tests"""
    print("🎯 Holly AI Advanced Features Test Suite")
    print("=" * 60)
    
    try:
        # Test individual advanced capabilities
        await test_market_regime_detection()
        await test_time_series_embeddings()
        await test_regime_based_strategy_adaptation()
        await test_embedding_pattern_matching()
        
        # Test integrated advanced workflow
        await test_integrated_advanced_workflow()
        
        print("\n✅ All advanced tests completed!")
        print("\n🎉 Holly AI Advanced Capabilities Summary:")
        print("1. ✅ Market Regime Detection - AI-powered regime classification")
        print("2. ✅ Time-Series Embeddings - Advanced pattern matching and forecasting")
        print("3. ✅ Regime-Based Strategy Adaptation - Dynamic strategy recommendations")
        print("4. ✅ Pattern Matching - Historical similarity analysis")
        print("5. ✅ Integrated Advanced Workflow - Multiple AI systems working together")
        
        print("\n💡 Advanced Features Enable:")
        print("- Automatic strategy adaptation based on market conditions")
        print("- Historical pattern recognition for better predictions")
        print("- Risk assessment using multiple AI models")
        print("- Context-aware trading recommendations")
        print("- Regime-specific stop loss and position sizing")
        
        print("\n🚀 Next Level Capabilities:")
        print("- Holly now understands market context at a deep level")
        print("- Combines technical analysis with regime awareness")
        print("- Provides historically-informed trading decisions")
        print("- Adapts strategies automatically to market conditions")
        
    except Exception as e:
        print(f"\n❌ Advanced test suite failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
