"""
Web search service for <PERSON> AI - provides web search capabilities when internal data is insufficient
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus

from src.core.config import settings
from src.core.logging import get_logger


class WebSearchService:
    """Service for performing web searches using multiple search providers"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Search provider configurations
        self.providers = {
            "google": {
                "enabled": hasattr(settings, 'GOOGLE_SEARCH_API_KEY') and hasattr(settings, 'GOOGLE_SEARCH_ENGINE_ID'),
                "api_key": getattr(settings, 'GOOGLE_SEARCH_API_KEY', None),
                "engine_id": getattr(settings, 'GOOGLE_SEARCH_ENGINE_ID', None),
                "base_url": "https://www.googleapis.com/customsearch/v1"
            },
            "bing": {
                "enabled": hasattr(settings, 'BING_SEARCH_API_KEY'),
                "api_key": getattr(settings, 'BING_SEARCH_API_KEY', None),
                "base_url": "https://api.bing.microsoft.com/v7.0/search"
            },
            "duckduckgo": {
                "enabled": True,  # DuckDuckGo doesn't require API key for basic search
                "base_url": "https://api.duckduckgo.com/"
            }
        }
        
        # Default to first available provider
        self.primary_provider = self._get_primary_provider()
        
    def _get_primary_provider(self) -> str:
        """Get the primary search provider based on available configurations"""
        for provider, config in self.providers.items():
            if config.get("enabled"):
                self.logger.info(f"Using {provider} as primary search provider")
                return provider
        
        self.logger.warning("No search providers configured - web search will be limited")
        return "duckduckgo"  # Fallback to DuckDuckGo
        
    async def search(self, query: str, num_results: int = 5, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform web search using the specified or primary provider
        
        Args:
            query: Search query string
            num_results: Number of results to return (1-10)
            provider: Specific provider to use (optional)
            
        Returns:
            Dict containing search results and metadata
        """
        try:
            # Use specified provider or fall back to primary
            search_provider = provider or self.primary_provider
            
            if search_provider not in self.providers:
                raise ValueError(f"Unknown search provider: {search_provider}")
                
            if not self.providers[search_provider].get("enabled"):
                raise ValueError(f"Search provider {search_provider} is not enabled")
            
            # Validate and sanitize inputs
            query = query.strip()
            if not query:
                raise ValueError("Search query cannot be empty")
                
            num_results = max(1, min(num_results, 10))  # Clamp between 1-10
            
            # Perform search based on provider
            if search_provider == "google":
                results = await self._search_google(query, num_results)
            elif search_provider == "bing":
                results = await self._search_bing(query, num_results)
            elif search_provider == "duckduckgo":
                results = await self._search_duckduckgo(query, num_results)
            else:
                raise ValueError(f"Search method not implemented for {search_provider}")
            
            # Add metadata
            results.update({
                "query": query,
                "provider": search_provider,
                "timestamp": datetime.utcnow().isoformat(),
                "requested_results": num_results
            })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error performing web search: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "provider": search_provider if 'search_provider' in locals() else "unknown",
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def _search_google(self, query: str, num_results: int) -> Dict[str, Any]:
        """Search using Google Custom Search API"""
        try:
            config = self.providers["google"]
            
            params = {
                "key": config["api_key"],
                "cx": config["engine_id"],
                "q": query,
                "num": num_results,
                "safe": "active",
                "fields": "items(title,link,snippet,displayLink)"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(config["base_url"], params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        results = []
                        for item in data.get("items", []):
                            results.append({
                                "title": item.get("title", ""),
                                "url": item.get("link", ""),
                                "snippet": item.get("snippet", ""),
                                "source": item.get("displayLink", ""),
                                "relevance_score": 1.0  # Google doesn't provide explicit scores
                            })
                        
                        return {
                            "success": True,
                            "results": results,
                            "total_results": len(results)
                        }
                    else:
                        error_data = await response.text()
                        raise Exception(f"Google Search API error {response.status}: {error_data}")
                        
        except Exception as e:
            self.logger.error(f"Google search failed: {e}")
            raise
    
    async def _search_bing(self, query: str, num_results: int) -> Dict[str, Any]:
        """Search using Bing Search API"""
        try:
            config = self.providers["bing"]
            
            headers = {
                "Ocp-Apim-Subscription-Key": config["api_key"]
            }
            
            params = {
                "q": query,
                "count": num_results,
                "safeSearch": "Moderate",
                "textFormat": "Raw"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(config["base_url"], headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        results = []
                        for item in data.get("webPages", {}).get("value", []):
                            results.append({
                                "title": item.get("name", ""),
                                "url": item.get("url", ""),
                                "snippet": item.get("snippet", ""),
                                "source": item.get("displayUrl", ""),
                                "relevance_score": 1.0  # Bing doesn't provide explicit scores in basic API
                            })
                        
                        return {
                            "success": True,
                            "results": results,
                            "total_results": len(results)
                        }
                    else:
                        error_data = await response.text()
                        raise Exception(f"Bing Search API error {response.status}: {error_data}")
                        
        except Exception as e:
            self.logger.error(f"Bing search failed: {e}")
            raise
    
    async def _search_duckduckgo(self, query: str, num_results: int) -> Dict[str, Any]:
        """Search using DuckDuckGo Instant Answer API (limited functionality)"""
        try:
            # Note: DuckDuckGo's free API is very limited and doesn't provide web search results
            # This is a fallback implementation that provides basic information
            
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.providers["duckduckgo"]["base_url"], params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        results = []
                        
                        # Add instant answer if available
                        if data.get("AbstractText"):
                            results.append({
                                "title": data.get("Heading", query),
                                "url": data.get("AbstractURL", ""),
                                "snippet": data.get("AbstractText", ""),
                                "source": data.get("AbstractSource", "DuckDuckGo"),
                                "relevance_score": 1.0
                            })
                        
                        # Add related topics
                        for topic in data.get("RelatedTopics", [])[:num_results-1]:
                            if isinstance(topic, dict) and topic.get("Text"):
                                results.append({
                                    "title": topic.get("Text", "").split(" - ")[0],
                                    "url": topic.get("FirstURL", ""),
                                    "snippet": topic.get("Text", ""),
                                    "source": "DuckDuckGo",
                                    "relevance_score": 0.8
                                })
                        
                        return {
                            "success": True,
                            "results": results[:num_results],
                            "total_results": len(results),
                            "note": "DuckDuckGo provides limited search results. Consider configuring Google or Bing for better results."
                        }
                    else:
                        raise Exception(f"DuckDuckGo API error {response.status}")
                        
        except Exception as e:
            self.logger.error(f"DuckDuckGo search failed: {e}")
            # Return empty results rather than failing completely
            return {
                "success": True,
                "results": [],
                "total_results": 0,
                "note": f"DuckDuckGo search failed: {e}. Consider configuring a different search provider."
            }
    
    async def search_financial_news(self, symbol: str, num_results: int = 5) -> Dict[str, Any]:
        """Specialized search for financial news about a specific symbol"""
        financial_query = f"{symbol} stock news earnings financial results"
        return await self.search(financial_query, num_results)
    
    async def search_market_events(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """Specialized search for market events and economic news"""
        market_query = f"{query} market impact trading economic news"
        return await self.search(market_query, num_results)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available search providers"""
        return [provider for provider, config in self.providers.items() if config.get("enabled")]
    
    def is_configured(self) -> bool:
        """Check if at least one search provider is properly configured"""
        return any(config.get("enabled") for config in self.providers.values())
