#!/usr/bin/env python3
"""
Holly AI Streamlit Interface
Simple web interface using Streamlit for Holly AI trading system
"""

import streamlit as st
import asyncio
import sys
from pathlib import Path
import json
import pandas as pd
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain


# Page configuration
st.set_page_config(
    page_title="Holly AI Trading System",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize Holly AI
@st.cache_resource
def get_holly_brain():
    return HollyAIBrain()

holly = get_holly_brain()

# Initialize session state
if 'conversation_history' not in st.session_state:
    st.session_state.conversation_history = []
if 'last_response' not in st.session_state:
    st.session_state.last_response = None

# Main header
st.title("🤖 Holly AI Trading System")
st.markdown("### Advanced AI-Powered Trading Assistant")

# Sidebar
with st.sidebar:
    st.header("🔧 Holly AI Features")
    
    st.markdown("""
    **Core Capabilities:**
    - 💬 Natural language trading
    - 📊 Technical analysis
    - 🛡️ AI-enhanced stop losses
    - 📈 Trading plan generation
    
    **Advanced AI Features:**
    - 🌐 Web search integration
    - 📱 Social sentiment analysis
    - 🌊 Market regime detection
    - 🧠 Time-series embeddings
    - 📈 TTM Squeeze scanning
    """)
    
    st.markdown("---")
    
    # Quick actions
    st.subheader("⚡ Quick Actions")
    
    if st.button("📊 Market Regime Analysis"):
        st.session_state.quick_action = "What's the current market regime?"
    
    if st.button("📱 AAPL Sentiment"):
        st.session_state.quick_action = "Analyze social sentiment for AAPL"
    
    if st.button("🔍 Market News"):
        st.session_state.quick_action = "Search for latest market news"
    
    if st.button("📈 TTM Scan"):
        st.session_state.quick_action = "Scan SPY, QQQ, NVDA for TTM Squeeze signals"

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    st.header("💬 Chat with Holly AI")
    
    # Chat input
    user_input = st.text_input(
        "Ask Holly anything about trading:",
        placeholder="e.g., Make me $50 today, What's AAPL looking like?, Analyze market regime",
        key="chat_input"
    )
    
    # Handle quick actions
    if 'quick_action' in st.session_state:
        user_input = st.session_state.quick_action
        del st.session_state.quick_action
    
    # Process user input
    if user_input:
        with st.spinner("🤔 Holly is thinking..."):
            try:
                # Run async function
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                response = loop.run_until_complete(holly.process_user_message(user_input))
                loop.close()
                
                # Store in session state
                st.session_state.conversation_history.append({
                    'user': user_input,
                    'holly': response,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                })
                st.session_state.last_response = response
                
                # Clear input
                st.session_state.chat_input = ""
                
            except Exception as e:
                st.error(f"Error: {e}")
    
    # Display conversation history
    st.subheader("📜 Conversation History")
    
    for i, conv in enumerate(reversed(st.session_state.conversation_history[-5:])):  # Last 5 conversations
        with st.expander(f"💬 {conv['timestamp']} - {conv['user'][:50]}..."):
            st.markdown(f"**You:** {conv['user']}")
            
            response = conv['holly']
            
            # Show function called
            if response.get('function_called'):
                st.info(f"🔧 Function Used: {response['function_called']}")
                
                # Show function results
                function_result = response.get('function_result', {})
                if function_result.get('success'):
                    st.success("✅ Function executed successfully")
                    
                    # Display specific results
                    if response['function_called'] == 'detect_market_regime':
                        regime_analysis = function_result.get('market_regime_analysis', {})
                        if regime_analysis:
                            st.metric("Market Regime", regime_analysis.get('current_regime', 'unknown'))
                            st.metric("Confidence", f"{regime_analysis.get('confidence', 0):.1%}")
                    
                    elif response['function_called'] == 'analyze_social_sentiment':
                        sentiment_analysis = function_result.get('analysis_summary', {})
                        if sentiment_analysis:
                            st.metric("Sentiment", sentiment_analysis.get('overall_sentiment', 'unknown'))
                            st.metric("Posts Analyzed", sentiment_analysis.get('total_posts_analyzed', 0))
                    
                else:
                    st.error(f"❌ Function failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response
            st.markdown(f"**🤖 Holly:** {response.get('response', 'No response')}")

with col2:
    st.header("📊 System Status")
    
    # System metrics
    st.metric("Functions Available", len(holly.functions))
    st.metric("Conversations", len(st.session_state.conversation_history))
    st.metric("Trading Mode", "Paper Trading")
    
    # Last response details
    if st.session_state.last_response:
        st.subheader("🔍 Last Response Details")
        
        response = st.session_state.last_response
        
        if response.get('function_called'):
            st.code(f"Function: {response['function_called']}")
            
            function_result = response.get('function_result', {})
            if function_result:
                # Show key metrics from function result
                if response['function_called'] == 'detect_market_regime':
                    regime_data = function_result.get('market_regime_analysis', {})
                    if regime_data:
                        st.json({
                            'regime': regime_data.get('current_regime'),
                            'confidence': regime_data.get('confidence'),
                            'duration_days': regime_data.get('regime_duration_days')
                        })
                
                elif response['function_called'] == 'scan_ttm_squeeze_signals':
                    scan_data = function_result.get('scan_results', {})
                    if scan_data:
                        st.json({
                            'signals_found': scan_data.get('signals_found', 0),
                            'symbols_scanned': scan_data.get('symbols_scanned', [])
                        })

# Advanced Features Tabs
st.markdown("---")
st.header("🧠 Advanced AI Features")

tab1, tab2, tab3, tab4, tab5 = st.tabs(["🌊 Regime", "📱 Sentiment", "🧠 Embeddings", "📈 TTM Scan", "🔍 News"])

with tab1:
    st.subheader("Market Regime Detection")
    
    col1, col2 = st.columns([1, 1])
    with col1:
        regime_symbols = st.text_input("Symbols to analyze:", "SPY,QQQ,VIX", key="regime_symbols")
    with col2:
        if st.button("Analyze Regime", key="regime_btn"):
            with st.spinner("Analyzing market regime..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    response = loop.run_until_complete(
                        holly.process_user_message(f"What's the current market regime for {regime_symbols}?")
                    )
                    loop.close()
                    
                    if response.get('function_result', {}).get('success'):
                        regime_analysis = response['function_result'].get('market_regime_analysis', {})
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Current Regime", regime_analysis.get('current_regime', 'unknown'))
                        with col2:
                            st.metric("Confidence", f"{regime_analysis.get('confidence', 0):.1%}")
                        with col3:
                            st.metric("Duration", f"{regime_analysis.get('regime_duration_days', 0)} days")
                        
                        st.markdown("**Holly's Analysis:**")
                        st.write(response.get('response', ''))
                    else:
                        st.error("Failed to analyze regime")
                        
                except Exception as e:
                    st.error(f"Error: {e}")

with tab2:
    st.subheader("Social Sentiment Analysis")
    
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        sentiment_symbol = st.text_input("Symbol:", "AAPL", key="sentiment_symbol")
    with col2:
        sentiment_hours = st.selectbox("Hours back:", [6, 12, 24, 48], index=2, key="sentiment_hours")
    with col3:
        if st.button("Analyze Sentiment", key="sentiment_btn"):
            with st.spinner("Analyzing social sentiment..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    response = loop.run_until_complete(
                        holly.process_user_message(f"Analyze social sentiment for {sentiment_symbol}")
                    )
                    loop.close()
                    
                    if response.get('function_result', {}).get('success'):
                        sentiment_analysis = response['function_result'].get('analysis_summary', {})
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Overall Sentiment", sentiment_analysis.get('overall_sentiment', 'unknown'))
                        with col2:
                            st.metric("Crowd Mood", sentiment_analysis.get('crowd_mood', 'unknown'))
                        with col3:
                            st.metric("Posts Analyzed", sentiment_analysis.get('total_posts_analyzed', 0))
                        
                        st.markdown("**Holly's Insights:**")
                        st.write(response.get('response', ''))
                    else:
                        st.error("Failed to analyze sentiment")
                        
                except Exception as e:
                    st.error(f"Error: {e}")

with tab3:
    st.subheader("Market Embeddings")
    
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        embedding_symbol = st.text_input("Primary Symbol:", "AAPL", key="embedding_symbol")
    with col2:
        embedding_context = st.text_input("Context Symbols:", "SPY,QQQ,VIX,TLT", key="embedding_context")
    with col3:
        if st.button("Generate Embeddings", key="embedding_btn"):
            with st.spinner("Generating market embeddings..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    response = loop.run_until_complete(
                        holly.process_user_message(f"Generate market embeddings for {embedding_symbol}")
                    )
                    loop.close()
                    
                    if response.get('function_result', {}).get('success'):
                        embedding_analysis = response['function_result']
                        pattern_analysis = embedding_analysis.get('pattern_analysis', {})
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Similar Patterns", pattern_analysis.get('similar_patterns_found', 0))
                        with col2:
                            st.metric("Pattern Confidence", f"{pattern_analysis.get('pattern_confidence', 0):.1%}")
                        
                        st.markdown("**Holly's Insights:**")
                        st.write(response.get('response', ''))
                    else:
                        st.error("Failed to generate embeddings")
                        
                except Exception as e:
                    st.error(f"Error: {e}")

with tab4:
    st.subheader("TTM Squeeze Scanner")
    
    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        ttm_symbols = st.text_input("Symbols to scan:", "AAPL,TSLA,NVDA", key="ttm_symbols")
    with col2:
        ttm_timeframe = st.selectbox("Timeframe:", ["1min", "5min", "15min", "30min", "1hour"], index=1, key="ttm_timeframe")
    with col3:
        if st.button("Scan TTM", key="ttm_btn"):
            with st.spinner("Scanning for TTM Squeeze signals..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    response = loop.run_until_complete(
                        holly.process_user_message(f"Scan {ttm_symbols} for TTM Squeeze signals on {ttm_timeframe}")
                    )
                    loop.close()
                    
                    if response.get('function_result', {}).get('success'):
                        scan_results = response['function_result'].get('scan_results', {})
                        
                        st.metric("Signals Found", scan_results.get('signals_found', 0))
                        
                        st.markdown("**Holly's Analysis:**")
                        st.write(response.get('response', ''))
                    else:
                        st.error("Failed to scan TTM signals")
                        
                except Exception as e:
                    st.error(f"Error: {e}")

with tab5:
    st.subheader("News Search")
    
    col1, col2 = st.columns([2, 1])
    with col1:
        news_query = st.text_input("Search query:", "market news today", key="news_query")
    with col2:
        if st.button("Search News", key="news_btn"):
            with st.spinner("Searching for news..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    response = loop.run_until_complete(
                        holly.process_user_message(f"Search for news about: {news_query}")
                    )
                    loop.close()
                    
                    if response.get('function_result', {}).get('success'):
                        news_results = response['function_result'].get('news_results', {})
                        
                        st.metric("Results Found", news_results.get('total_results', 0))
                        
                        st.markdown("**Holly's Analysis:**")
                        st.write(response.get('response', ''))
                    else:
                        st.error("Failed to search news")
                        
                except Exception as e:
                    st.error(f"Error: {e}")

# Footer
st.markdown("---")
st.markdown("🤖 **Holly AI Trading System** - Paper Trading Mode - Safe for Learning and Testing")
st.markdown("Built with advanced AI capabilities including regime detection, sentiment analysis, and pattern recognition.")
