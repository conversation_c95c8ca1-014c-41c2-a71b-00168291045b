import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createChart } from 'lightweight-charts';
import { Send, TrendingUp, BarChart3, Sparkles } from 'lucide-react';
import SpaceBackground from './SpaceBackground';

const AtlasInterface = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'system',
      content: "Hi! I'm H.O.L.L.Y, your AI trading assistant. I can help you with stock analysis, trading strategies, and market insights. Try asking me about any stock or trading question!",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);

  // Initialize chart when stock quote message is rendered
  useEffect(() => {
    const stockMessage = messages.find(msg => msg.type === 'stock-quote');
    if (stockMessage && chartRef.current && !chartInstanceRef.current) {
      initializeChart(stockMessage.chartData);
    }
  }, [messages]);

  const initializeChart = (data) => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.remove();
    }

    const chart = createChart(chartRef.current, {
      width: 280,
      height: 120,
      layout: {
        background: { color: 'transparent' },
        textColor: '#67e8f9',
      },
      grid: {
        vertLines: { color: 'rgba(6, 182, 212, 0.1)' },
        horzLines: { color: 'rgba(6, 182, 212, 0.1)' },
      },
      crosshair: {
        mode: 0,
      },
      rightPriceScale: {
        borderColor: 'rgba(6, 182, 212, 0.3)',
        textColor: '#67e8f9',
      },
      timeScale: {
        borderColor: 'rgba(6, 182, 212, 0.3)',
        textColor: '#67e8f9',
        timeVisible: false,
        secondsVisible: false,
      },
    });

    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#22d3ee',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#22d3ee',
      wickDownColor: '#ef4444',
      wickUpColor: '#22d3ee',
    });

    candlestickSeries.setData(data);
    chartInstanceRef.current = chart;

    // Auto-fit content
    chart.timeScale().fitContent();
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    const messageToSend = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      // Call the real Holly AI backend
      const response = await fetch('/api/v1/holly/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToSend,
          user_context: {
            timestamp: new Date().toISOString(),
            session_id: 'atlas_session',
            interface: 'atlas'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setIsTyping(false);

      // Create Holly's response message
      const hollyResponse = {
        id: Date.now() + 1,
        type: 'system',
        content: data.response || "I'm having trouble processing that request right now.",
        timestamp: new Date(),
        response_type: data.type,
        requires_action: data.requires_action,
        trading_plan: data.trading_plan,
        plan_id: data.plan_id,
        function_called: data.function_called
      };

      setMessages(prev => [...prev, hollyResponse]);

      // If Holly provided a stock quote, add it as a separate message
      if (data.type === 'stock_quote' && data.trading_plan) {
        const stockMessage = {
          id: Date.now() + 2,
          type: 'stock-quote',
          symbol: data.trading_plan.symbol || 'UNKNOWN',
          price: data.trading_plan.current_price || 0,
          change: data.trading_plan.price_change || 0,
          changePercent: data.trading_plan.price_change_percent || 0,
          company: data.trading_plan.company_name || 'Unknown Company',
          chartData: generateMockChartData(),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, stockMessage]);
      }

    } catch (error) {
      console.error('Error calling Holly AI:', error);
      setIsTyping(false);

      const errorMessage = {
        id: Date.now() + 1,
        type: 'system',
        content: "I'm having trouble connecting to my AI brain right now. Please try again in a moment.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen space-bg relative overflow-hidden">
      <SpaceBackground />
      
      {/* Main Container */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-full max-w-md"
        >
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-center mb-8"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 mb-4">
              <Sparkles className="w-8 h-8 text-cyan-400" />
            </div>
            <h1 className="text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-2">
              H.O.L.L.Y
            </h1>
            <p className="text-cyan-300/80 text-sm">
              Stock Analysis Chatbot
            </p>
          </motion.div>

          {/* Chat Container */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="glass-card p-6 space-y-6"
          >
            {/* Messages */}
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    {message.type === 'system' && (
                      <div className="text-cyan-100 text-sm leading-relaxed">
                        {message.content}
                      </div>
                    )}

                    {message.type === 'user' && (
                      <div className="text-right">
                        <div className="inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm">
                          {message.content}
                        </div>
                      </div>
                    )}

                    {message.type === 'stock-quote' && (
                      <div className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-4 space-y-4">
                        {/* Stock Header */}
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-2xl font-bold text-white">
                              {message.symbol}
                            </div>
                            <div className="text-cyan-300/80 text-sm">
                              {message.company}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-white">
                              {message.price}
                            </div>
                            <div className="flex items-center text-sm text-green-400">
                              <TrendingUp className="w-4 h-4 mr-1" />
                              +{message.change}%
                            </div>
                          </div>
                        </div>

                        {/* Chart */}
                        <div className="bg-black/20 rounded-xl p-3">
                          <div ref={chartRef} className="w-full" />
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <button className="flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow">
                            Show earnings
                          </button>
                          <button className="flex-1 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 text-cyan-300 px-3 py-2 rounded-xl text-sm transition-all duration-200 btn-glow">
                            Analyze trend
                          </button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing Indicator */}
              <AnimatePresence>
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-center space-x-2 text-cyan-400"
                  >
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span className="text-sm">H.O.L.L.Y is typing...</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Input Area */}
            <div className="relative">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Send a message..."
                className="w-full bg-black/20 border border-cyan-500/30 rounded-2xl px-4 py-3 pr-12 text-white placeholder-cyan-300/50 focus:outline-none focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-200"
              />
              <button
                onClick={handleSendMessage}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center hover:scale-105 transition-transform duration-200 btn-glow"
              >
                <Send className="w-4 h-4 text-white" />
              </button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

// Generate mock chart data
function generateMockChartData() {
  const data = [];
  let basePrice = 148;
  
  for (let i = 0; i < 50; i++) {
    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;
    const volatility = 0.02;
    const change = (Math.random() - 0.5) * volatility * basePrice;
    
    const open = basePrice;
    const close = basePrice + change;
    const high = Math.max(open, close) + Math.random() * 0.5;
    const low = Math.min(open, close) - Math.random() * 0.5;
    
    data.push({
      time,
      open: parseFloat(open.toFixed(2)),
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      close: parseFloat(close.toFixed(2)),
    });
    
    basePrice = close;
  }
  
  return data;
}

export default AtlasInterface;
