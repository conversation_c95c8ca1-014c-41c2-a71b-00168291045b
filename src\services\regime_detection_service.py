"""
Volatility Regime Detection Service for Holly AI
Uses machine learning to classify market regimes and adapt trading strategies
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from src.core.config import settings
from src.core.logging import get_logger
from src.services.fmp_service import FMPService


class MarketRegime(Enum):
    """Market regime classifications"""
    CALM = "calm"
    TURBULENT = "turbulent"
    TRENDING = "trending"
    MEAN_REVERTING = "mean_reverting"
    EVENT_DRIVEN = "event_driven"
    CRISIS = "crisis"


@dataclass
class RegimeFeatures:
    """Features used for regime classification"""
    realized_volatility: float
    volatility_percentile: float
    skew: float
    kurtosis: float
    volume_surge: float
    news_volume: int
    correlation_breakdown: float
    momentum_strength: float
    mean_reversion_strength: float
    vix_level: float
    vix_term_structure: float


@dataclass
class RegimeClassification:
    """Result of regime classification"""
    regime: MarketRegime
    confidence: float
    features: RegimeFeatures
    timestamp: datetime
    duration_days: int
    regime_change_probability: float
    recommended_strategies: List[str]
    risk_adjustment: float


class VolatilityRegimeDetector:
    """Advanced volatility regime detection using multiple features and ML"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.fmp_service = None
        
        # Regime detection parameters
        self.lookback_days = 252  # 1 year for regime analysis
        self.short_window = 20    # Short-term volatility window
        self.long_window = 60     # Long-term volatility window
        
        # Regime thresholds (will be dynamically adjusted)
        self.volatility_thresholds = {
            'low': 0.15,      # 15th percentile
            'medium': 0.50,   # 50th percentile  
            'high': 0.85,     # 85th percentile
            'extreme': 0.95   # 95th percentile
        }
        
        # Feature weights for regime classification
        self.feature_weights = {
            'volatility': 0.25,
            'momentum': 0.20,
            'mean_reversion': 0.15,
            'correlation': 0.15,
            'volume': 0.10,
            'news': 0.10,
            'vix': 0.05
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.fmp_service = FMPService()
        await self.fmp_service.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.fmp_service:
            await self.fmp_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def detect_current_regime(self, symbols: List[str] = None) -> Dict[str, Any]:
        """
        Detect the current market regime across multiple assets
        
        Args:
            symbols: List of symbols to analyze (defaults to major indices)
        """
        if symbols is None:
            symbols = ['SPY', 'QQQ', 'IWM', 'VIX']  # Major market indices
        
        try:
            # Gather data for all symbols
            market_data = {}
            for symbol in symbols:
                data = await self._fetch_regime_data(symbol)
                if data is not None:
                    market_data[symbol] = data
            
            if not market_data:
                return {
                    'success': False,
                    'error': 'No market data available for regime detection'
                }
            
            # Calculate regime features
            regime_features = await self._calculate_regime_features(market_data)
            
            # Classify regime using ML approach
            regime_classification = await self._classify_regime(regime_features)
            
            # Generate strategy recommendations
            strategy_recommendations = self._generate_strategy_recommendations(regime_classification)
            
            # Calculate regime persistence and change probability
            regime_persistence = await self._calculate_regime_persistence(market_data)
            
            return {
                'success': True,
                'current_regime': regime_classification.regime.value,
                'confidence': regime_classification.confidence,
                'regime_duration_days': regime_classification.duration_days,
                'regime_change_probability': regime_classification.regime_change_probability,
                'features': {
                    'realized_volatility': regime_features.realized_volatility,
                    'volatility_percentile': regime_features.volatility_percentile,
                    'momentum_strength': regime_features.momentum_strength,
                    'mean_reversion_strength': regime_features.mean_reversion_strength,
                    'volume_surge': regime_features.volume_surge,
                    'correlation_breakdown': regime_features.correlation_breakdown,
                    'vix_level': regime_features.vix_level
                },
                'strategy_recommendations': strategy_recommendations,
                'risk_adjustment': regime_classification.risk_adjustment,
                'regime_persistence': regime_persistence,
                'symbols_analyzed': list(market_data.keys()),
                'timestamp': datetime.utcnow().isoformat(),
                'next_update_recommended': (datetime.utcnow() + timedelta(hours=6)).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
            return {
                'success': False,
                'error': str(e),
                'fallback_regime': 'calm',
                'fallback_confidence': 0.5
            }
    
    async def _fetch_regime_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Fetch historical data needed for regime analysis"""
        try:
            # Get daily data for regime analysis
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.lookback_days + 50)  # Extra buffer
            
            endpoint = f"v3/historical-price-full/{symbol}"
            params = {
                'from': start_date.strftime('%Y-%m-%d'),
                'to': end_date.strftime('%Y-%m-%d')
            }
            
            response = await self.fmp_service.get_data(endpoint, params)
            
            if not response or 'historical' not in response:
                self.logger.warning(f"No historical data for {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(response['historical'])
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            
            # Calculate returns and volatility
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            
            # Rolling volatility calculations
            df['realized_vol_20'] = df['returns'].rolling(window=20).std() * np.sqrt(252)
            df['realized_vol_60'] = df['returns'].rolling(window=60).std() * np.sqrt(252)
            
            # Volume analysis
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            return df.dropna()
            
        except Exception as e:
            self.logger.error(f"Error fetching regime data for {symbol}: {e}")
            return None
    
    async def _calculate_regime_features(self, market_data: Dict[str, pd.DataFrame]) -> RegimeFeatures:
        """Calculate features used for regime classification"""
        try:
            # Aggregate features across all symbols
            all_returns = []
            all_volatilities = []
            all_volume_ratios = []
            
            for symbol, df in market_data.items():
                if symbol != 'VIX':  # VIX handled separately
                    all_returns.extend(df['returns'].dropna().tolist())
                    all_volatilities.extend(df['realized_vol_20'].dropna().tolist())
                    all_volume_ratios.extend(df['volume_ratio'].dropna().tolist())
            
            # Calculate aggregate statistics
            returns_array = np.array(all_returns)
            vol_array = np.array(all_volatilities)
            volume_array = np.array(all_volume_ratios)
            
            # Realized volatility (current)
            realized_volatility = np.std(returns_array[-20:]) * np.sqrt(252) if len(returns_array) >= 20 else 0.2
            
            # Volatility percentile (where current vol sits historically)
            volatility_percentile = np.percentile(vol_array, 50) if len(vol_array) > 0 else 0.5
            current_vol_percentile = (realized_volatility > vol_array).mean() if len(vol_array) > 0 else 0.5
            
            # Skew and kurtosis
            skew = self._calculate_skew(returns_array[-60:]) if len(returns_array) >= 60 else 0.0
            kurtosis = self._calculate_kurtosis(returns_array[-60:]) if len(returns_array) >= 60 else 3.0
            
            # Volume surge
            volume_surge = np.mean(volume_array[-5:]) if len(volume_array) >= 5 else 1.0
            
            # News volume (placeholder - would integrate with news API)
            news_volume = 10  # Default moderate news volume
            
            # Correlation breakdown
            correlation_breakdown = await self._calculate_correlation_breakdown(market_data)
            
            # Momentum and mean reversion strength
            momentum_strength = self._calculate_momentum_strength(market_data)
            mean_reversion_strength = self._calculate_mean_reversion_strength(market_data)
            
            # VIX analysis
            vix_level, vix_term_structure = self._analyze_vix(market_data.get('VIX'))
            
            return RegimeFeatures(
                realized_volatility=realized_volatility,
                volatility_percentile=current_vol_percentile,
                skew=skew,
                kurtosis=kurtosis,
                volume_surge=volume_surge,
                news_volume=news_volume,
                correlation_breakdown=correlation_breakdown,
                momentum_strength=momentum_strength,
                mean_reversion_strength=mean_reversion_strength,
                vix_level=vix_level,
                vix_term_structure=vix_term_structure
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating regime features: {e}")
            # Return default features
            return RegimeFeatures(
                realized_volatility=0.2,
                volatility_percentile=0.5,
                skew=0.0,
                kurtosis=3.0,
                volume_surge=1.0,
                news_volume=10,
                correlation_breakdown=0.5,
                momentum_strength=0.5,
                mean_reversion_strength=0.5,
                vix_level=20.0,
                vix_term_structure=0.0
            )
    
    async def _classify_regime(self, features: RegimeFeatures) -> RegimeClassification:
        """Classify market regime using feature analysis and LLM"""
        try:
            # Rule-based classification with ML enhancement
            regime_scores = {
                MarketRegime.CALM: 0.0,
                MarketRegime.TURBULENT: 0.0,
                MarketRegime.TRENDING: 0.0,
                MarketRegime.MEAN_REVERTING: 0.0,
                MarketRegime.EVENT_DRIVEN: 0.0,
                MarketRegime.CRISIS: 0.0
            }
            
            # Volatility-based scoring
            if features.realized_volatility < 0.15:
                regime_scores[MarketRegime.CALM] += 0.4
            elif features.realized_volatility > 0.35:
                regime_scores[MarketRegime.TURBULENT] += 0.3
                regime_scores[MarketRegime.CRISIS] += 0.2
            
            # Momentum-based scoring
            if features.momentum_strength > 0.7:
                regime_scores[MarketRegime.TRENDING] += 0.3
            elif features.mean_reversion_strength > 0.7:
                regime_scores[MarketRegime.MEAN_REVERTING] += 0.3
            
            # Volume and correlation scoring
            if features.volume_surge > 2.0 and features.correlation_breakdown > 0.7:
                regime_scores[MarketRegime.EVENT_DRIVEN] += 0.4
            
            # VIX-based scoring
            if features.vix_level > 30:
                regime_scores[MarketRegime.CRISIS] += 0.3
                regime_scores[MarketRegime.TURBULENT] += 0.2
            elif features.vix_level < 15:
                regime_scores[MarketRegime.CALM] += 0.3
            
            # Use LLM for final classification
            llm_classification = await self._llm_regime_classification(features, regime_scores)
            
            # Determine final regime
            best_regime = max(regime_scores.items(), key=lambda x: x[1])
            confidence = best_regime[1]
            
            # Adjust with LLM input
            if llm_classification.get('regime'):
                try:
                    llm_regime = MarketRegime(llm_classification['regime'])
                    llm_confidence = llm_classification.get('confidence', 0.5)
                    
                    # Blend rule-based and LLM classification
                    if llm_confidence > 0.7:
                        final_regime = llm_regime
                        final_confidence = (confidence + llm_confidence) / 2
                    else:
                        final_regime = best_regime[0]
                        final_confidence = confidence
                except:
                    final_regime = best_regime[0]
                    final_confidence = confidence
            else:
                final_regime = best_regime[0]
                final_confidence = confidence
            
            # Calculate regime change probability
            regime_change_prob = self._calculate_regime_change_probability(features, final_regime)
            
            # Determine risk adjustment
            risk_adjustment = self._calculate_risk_adjustment(final_regime, features)
            
            return RegimeClassification(
                regime=final_regime,
                confidence=min(final_confidence, 1.0),
                features=features,
                timestamp=datetime.utcnow(),
                duration_days=7,  # Placeholder - would track actual duration
                regime_change_probability=regime_change_prob,
                recommended_strategies=self._get_regime_strategies(final_regime),
                risk_adjustment=risk_adjustment
            )
            
        except Exception as e:
            self.logger.error(f"Error classifying regime: {e}")
            return RegimeClassification(
                regime=MarketRegime.CALM,
                confidence=0.5,
                features=features,
                timestamp=datetime.utcnow(),
                duration_days=7,
                regime_change_probability=0.3,
                recommended_strategies=['balanced'],
                risk_adjustment=1.0
            )
    
    async def _llm_regime_classification(self, features: RegimeFeatures, rule_scores: Dict) -> Dict[str, Any]:
        """Use LLM to enhance regime classification"""
        try:
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            
            # Prepare feature summary for LLM
            feature_summary = {
                'realized_volatility': round(features.realized_volatility, 3),
                'volatility_percentile': round(features.volatility_percentile, 3),
                'momentum_strength': round(features.momentum_strength, 3),
                'mean_reversion_strength': round(features.mean_reversion_strength, 3),
                'volume_surge': round(features.volume_surge, 2),
                'correlation_breakdown': round(features.correlation_breakdown, 3),
                'vix_level': round(features.vix_level, 1),
                'skew': round(features.skew, 3),
                'kurtosis': round(features.kurtosis, 3)
            }
            
            rule_summary = {regime.value: round(score, 3) for regime, score in rule_scores.items()}
            
            prompt = f"""
            Analyze these market regime features and classify the current market regime:
            
            Features: {json.dumps(feature_summary, indent=2)}
            Rule-based scores: {json.dumps(rule_summary, indent=2)}
            
            Market regimes:
            - calm: Low volatility, normal correlations, steady trends
            - turbulent: High volatility, but no clear direction
            - trending: Strong momentum, directional moves
            - mean_reverting: Oscillating markets, reversals
            - event_driven: News/event driven moves, correlation breakdown
            - crisis: Extreme volatility, flight to quality
            
            Return JSON with:
            - regime: one of the regime types above
            - confidence: 0-1 confidence score
            - reasoning: brief explanation
            - key_factors: list of most important factors
            """
            
            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=300
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            self.logger.error(f"Error in LLM regime classification: {e}")
            return {}
    
    def _calculate_skew(self, returns: np.ndarray) -> float:
        """Calculate skewness of returns"""
        if len(returns) < 3:
            return 0.0
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        if std_return == 0:
            return 0.0
        return np.mean(((returns - mean_return) / std_return) ** 3)
    
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate kurtosis of returns"""
        if len(returns) < 4:
            return 3.0
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        if std_return == 0:
            return 3.0
        return np.mean(((returns - mean_return) / std_return) ** 4)
    
    async def _calculate_correlation_breakdown(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """Calculate correlation breakdown indicator"""
        try:
            if len(market_data) < 2:
                return 0.5
            
            # Get returns for correlation calculation
            returns_data = {}
            for symbol, df in market_data.items():
                if symbol != 'VIX' and 'returns' in df.columns:
                    returns_data[symbol] = df['returns'].dropna()
            
            if len(returns_data) < 2:
                return 0.5
            
            # Calculate rolling correlations
            min_length = min(len(data) for data in returns_data.values())
            if min_length < 60:
                return 0.5
            
            # Recent correlation vs historical
            recent_corr = self._calculate_avg_correlation(returns_data, window=-20)
            historical_corr = self._calculate_avg_correlation(returns_data, window=-60)
            
            # Correlation breakdown = how much correlations have changed
            breakdown = abs(recent_corr - historical_corr) / (historical_corr + 0.01)
            return min(breakdown, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation breakdown: {e}")
            return 0.5
    
    def _calculate_avg_correlation(self, returns_data: Dict, window: int) -> float:
        """Calculate average correlation between assets"""
        try:
            symbols = list(returns_data.keys())
            correlations = []
            
            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    data1 = returns_data[symbols[i]].iloc[window:]
                    data2 = returns_data[symbols[j]].iloc[window:]
                    
                    if len(data1) > 10 and len(data2) > 10:
                        corr = np.corrcoef(data1, data2)[0, 1]
                        if not np.isnan(corr):
                            correlations.append(abs(corr))
            
            return np.mean(correlations) if correlations else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating average correlation: {e}")
            return 0.5
    
    def _calculate_momentum_strength(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """Calculate overall momentum strength across assets"""
        try:
            momentum_scores = []
            
            for symbol, df in market_data.items():
                if symbol != 'VIX' and len(df) >= 20:
                    # Price momentum
                    price_momentum = (df['close'].iloc[-1] / df['close'].iloc[-20] - 1)
                    
                    # Trend consistency
                    returns_20 = df['returns'].iloc[-20:]
                    positive_days = (returns_20 > 0).sum()
                    trend_consistency = positive_days / 20
                    
                    # Combined momentum score
                    momentum_score = (abs(price_momentum) * 0.7 + trend_consistency * 0.3)
                    momentum_scores.append(momentum_score)
            
            return np.mean(momentum_scores) if momentum_scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum strength: {e}")
            return 0.5
    
    def _calculate_mean_reversion_strength(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """Calculate mean reversion strength"""
        try:
            reversion_scores = []
            
            for symbol, df in market_data.items():
                if symbol != 'VIX' and len(df) >= 40:
                    # Calculate how often price reverts to mean
                    returns = df['returns'].iloc[-40:]
                    mean_return = returns.mean()
                    
                    # Count reversions
                    above_mean = returns > mean_return
                    reversions = 0
                    for i in range(1, len(above_mean)):
                        if above_mean.iloc[i] != above_mean.iloc[i-1]:
                            reversions += 1
                    
                    reversion_rate = reversions / (len(above_mean) - 1)
                    reversion_scores.append(reversion_rate)
            
            return np.mean(reversion_scores) if reversion_scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating mean reversion strength: {e}")
            return 0.5
    
    def _analyze_vix(self, vix_data: Optional[pd.DataFrame]) -> Tuple[float, float]:
        """Analyze VIX level and term structure"""
        try:
            if vix_data is None or len(vix_data) < 20:
                return 20.0, 0.0  # Default values
            
            current_vix = vix_data['close'].iloc[-1]
            vix_ma = vix_data['close'].rolling(window=20).mean().iloc[-1]
            
            # Term structure approximation (current vs moving average)
            term_structure = (current_vix - vix_ma) / vix_ma
            
            return float(current_vix), float(term_structure)
            
        except Exception as e:
            self.logger.error(f"Error analyzing VIX: {e}")
            return 20.0, 0.0
    
    def _calculate_regime_change_probability(self, features: RegimeFeatures, current_regime: MarketRegime) -> float:
        """Calculate probability of regime change"""
        try:
            # Base probability
            base_prob = 0.1  # 10% base chance of regime change
            
            # Adjust based on volatility
            if features.realized_volatility > 0.3:
                base_prob += 0.2
            
            # Adjust based on volume surge
            if features.volume_surge > 2.0:
                base_prob += 0.15
            
            # Adjust based on correlation breakdown
            if features.correlation_breakdown > 0.7:
                base_prob += 0.15
            
            # Adjust based on VIX
            if features.vix_level > 25 or features.vix_level < 12:
                base_prob += 0.1
            
            return min(base_prob, 0.8)  # Cap at 80%
            
        except Exception as e:
            self.logger.error(f"Error calculating regime change probability: {e}")
            return 0.2
    
    def _get_regime_strategies(self, regime: MarketRegime) -> List[str]:
        """Get recommended strategies for each regime"""
        strategy_map = {
            MarketRegime.CALM: ['mean_reversion', 'carry_trades', 'volatility_selling'],
            MarketRegime.TURBULENT: ['momentum', 'volatility_trading', 'defensive'],
            MarketRegime.TRENDING: ['momentum', 'trend_following', 'breakout'],
            MarketRegime.MEAN_REVERTING: ['mean_reversion', 'pairs_trading', 'contrarian'],
            MarketRegime.EVENT_DRIVEN: ['news_trading', 'volatility_trading', 'sector_rotation'],
            MarketRegime.CRISIS: ['defensive', 'flight_to_quality', 'volatility_buying']
        }
        return strategy_map.get(regime, ['balanced'])
    
    def _calculate_risk_adjustment(self, regime: MarketRegime, features: RegimeFeatures) -> float:
        """Calculate risk adjustment factor for the regime"""
        risk_adjustments = {
            MarketRegime.CALM: 1.0,
            MarketRegime.TURBULENT: 0.7,
            MarketRegime.TRENDING: 1.2,
            MarketRegime.MEAN_REVERTING: 0.9,
            MarketRegime.EVENT_DRIVEN: 0.6,
            MarketRegime.CRISIS: 0.4
        }
        
        base_adjustment = risk_adjustments.get(regime, 1.0)
        
        # Further adjust based on volatility
        vol_adjustment = 1.0 - (features.realized_volatility - 0.2) * 0.5
        vol_adjustment = max(0.3, min(1.5, vol_adjustment))
        
        return base_adjustment * vol_adjustment
    
    def _generate_strategy_recommendations(self, classification: RegimeClassification) -> List[Dict[str, Any]]:
        """Generate detailed strategy recommendations"""
        recommendations = []
        
        for strategy in classification.recommended_strategies:
            rec = {
                'strategy': strategy,
                'confidence': classification.confidence,
                'risk_adjustment': classification.risk_adjustment,
                'regime_context': classification.regime.value
            }
            
            # Add strategy-specific details
            if strategy == 'momentum':
                rec['details'] = 'Focus on trend-following strategies, breakout trades'
                rec['timeframe'] = 'medium_term'
            elif strategy == 'mean_reversion':
                rec['details'] = 'Look for oversold/overbought conditions, range trading'
                rec['timeframe'] = 'short_term'
            elif strategy == 'defensive':
                rec['details'] = 'Reduce position sizes, increase cash, hedge exposure'
                rec['timeframe'] = 'immediate'
            elif strategy == 'volatility_trading':
                rec['details'] = 'Trade volatility directly, straddles, strangles'
                rec['timeframe'] = 'short_term'
            
            recommendations.append(rec)
        
        return recommendations
    
    async def _calculate_regime_persistence(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Calculate how persistent the current regime is likely to be"""
        try:
            # Simplified persistence calculation
            # In practice, this would use historical regime transitions
            
            avg_volatility = np.mean([
                df['realized_vol_20'].iloc[-1] 
                for df in market_data.values() 
                if 'realized_vol_20' in df.columns and len(df) > 0
            ])
            
            # Higher volatility = less persistent
            persistence_score = max(0.2, 1.0 - (avg_volatility - 0.15) * 2)
            
            expected_duration_days = int(persistence_score * 30)  # 1-30 days
            
            return {
                'persistence_score': persistence_score,
                'expected_duration_days': expected_duration_days,
                'stability_factors': ['volatility_level', 'correlation_stability']
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating regime persistence: {e}")
            return {
                'persistence_score': 0.5,
                'expected_duration_days': 15,
                'stability_factors': ['unknown']
            }

    async def get_regime_for_symbol(self, symbol: str) -> Dict[str, Any]:
        """Get regime classification for a specific symbol"""
        try:
            regime_data = await self.detect_current_regime([symbol])

            if not regime_data.get('success'):
                return regime_data

            # Add symbol-specific insights
            regime_data['symbol'] = symbol
            regime_data['symbol_specific_insights'] = await self._get_symbol_regime_insights(symbol, regime_data)

            return regime_data

        except Exception as e:
            self.logger.error(f"Error getting regime for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol
            }

    async def _get_symbol_regime_insights(self, symbol: str, regime_data: Dict) -> List[str]:
        """Get symbol-specific insights based on regime"""
        insights = []

        try:
            regime = regime_data.get('current_regime', 'calm')
            confidence = regime_data.get('confidence', 0.5)

            if regime == 'trending' and confidence > 0.7:
                insights.append(f"{symbol} is in a strong trending regime - consider momentum strategies")
            elif regime == 'mean_reverting':
                insights.append(f"{symbol} shows mean-reverting behavior - look for oversold/overbought levels")
            elif regime == 'turbulent':
                insights.append(f"{symbol} is in turbulent conditions - reduce position size and use wider stops")
            elif regime == 'crisis':
                insights.append(f"{symbol} is in crisis mode - consider defensive positioning")

            # Add volatility insights
            vol_percentile = regime_data.get('features', {}).get('volatility_percentile', 0.5)
            if vol_percentile > 0.8:
                insights.append(f"{symbol} volatility is in top 20% - expect larger price moves")
            elif vol_percentile < 0.2:
                insights.append(f"{symbol} volatility is unusually low - potential for volatility expansion")

        except Exception as e:
            self.logger.error(f"Error generating symbol insights: {e}")
            insights.append("Regime analysis completed - use with technical analysis")

        return insights
