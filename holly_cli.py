#!/usr/bin/env python3
"""
Holly AI Command Line Interface
Interactive terminal interface for Holly AI trading system
"""

import asyncio
import sys
import os
from pathlib import Path
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain
from src.core.config import settings


class HollyCLI:
    """Command line interface for Holly AI"""
    
    def __init__(self):
        self.holly = HollyAIBrain()
        self.conversation_history = []
        self.running = True
        
    def print_banner(self):
        """Print Holly AI banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🤖 Holly AI Trading System                ║
║                     Command Line Interface                   ║
╠══════════════════════════════════════════════════════════════╣
║  Enhanced with Advanced AI Capabilities:                    ║
║  • Web Search Integration                                    ║
║  • Social Sentiment Analysis                                 ║
║  • Market Regime Detection                                   ║
║  • Time-Series Embeddings                                    ║
║  • TTM Squeeze Pattern Recognition                           ║
║  • AI-Enhanced Stop Loss Calculation                        ║
╚══════════════════════════════════════════════════════════════╝

Type 'help' for commands, 'quit' to exit
Paper Trading Mode - Safe for learning and testing
"""
        print(banner)
    
    def print_help(self):
        """Print available commands"""
        help_text = """
🤖 Holly AI Commands:

Basic Trading:
  make me $50 today              - Create a trading plan for $50 profit
  find momentum plays            - Scan for momentum opportunities
  what's AAPL looking like?      - Analyze a specific stock
  hedge my TSLA position         - Create hedging strategies

Advanced AI Features:
  regime analysis                - Detect current market regime
  sentiment AAPL                 - Analyze social sentiment for AAPL
  embeddings TSLA               - Generate market embeddings for TSLA
  ttm scan AAPL,NVDA,MSFT       - Scan for TTM Squeeze signals
  search Apple earnings          - Search web for current news

System Commands:
  help                          - Show this help message
  history                       - Show conversation history
  clear                         - Clear screen
  status                        - Show system status
  config                        - Show configuration
  quit / exit                   - Exit Holly AI

Examples:
  > make me $100 today with low risk
  > what's the market regime right now?
  > analyze social sentiment for Tesla
  > scan for TTM squeeze signals in tech stocks
  > search for Fed meeting news
"""
        print(help_text)
    
    def print_status(self):
        """Print system status"""
        status = f"""
🔧 Holly AI System Status:

Configuration:
  • OpenAI API: {'✅ Configured' if settings.OPENAI_API_KEY else '❌ Missing'}
  • FMP API: {'✅ Configured' if settings.FMP_API_KEY else '❌ Missing'}
  • Alpaca API: {'✅ Configured' if settings.APCA_API_KEY_ID else '❌ Missing'}
  • Google Search: {'✅ Configured' if getattr(settings, 'GOOGLE_SEARCH_API_KEY', None) else '❌ Missing'}

Functions Available: {len(self.holly.functions)}
Conversation History: {len(self.conversation_history)} messages
Trading Mode: Paper Trading (Safe)
"""
        print(status)
    
    def print_config(self):
        """Print configuration details"""
        config = f"""
⚙️ Holly AI Configuration:

API Endpoints:
  • OpenAI Model: {getattr(settings, 'OPENAI_MODEL', 'gpt-4')}
  • FMP Base URL: {getattr(settings, 'FMP_BASE_URL', 'https://financialmodelingprep.com/api')}
  • Alpaca Base URL: {getattr(settings, 'APCA_API_BASE_URL', 'https://paper-api.alpaca.markets')}

Advanced Features:
  • Web Search: {'Enabled' if getattr(settings, 'GOOGLE_SEARCH_API_KEY', None) else 'Disabled'}
  • Social Sentiment: Enabled
  • Regime Detection: Enabled
  • Time-Series Embeddings: Enabled
  • TTM Squeeze Scanner: Enabled

Risk Management:
  • Default Risk: {getattr(settings, 'DEFAULT_RISK_PERCENT', 2.0)}%
  • Max Positions: {getattr(settings, 'MAX_POSITIONS', 10)}
  • Paper Trading: Enabled
"""
        print(config)
    
    def format_response(self, response):
        """Format Holly's response for terminal display"""
        output = []
        
        # Function called
        if response.get("function_called"):
            output.append(f"🔧 Function Used: {response['function_called']}")
            
            # Show key results
            function_result = response.get("function_result", {})
            if function_result.get("success"):
                output.append("✅ Function executed successfully")
                
                # Specific formatting for different functions
                if response["function_called"] == "detect_market_regime":
                    regime_analysis = function_result.get("market_regime_analysis", {})
                    if regime_analysis:
                        output.append(f"📊 Market Regime: {regime_analysis.get('current_regime', 'unknown')}")
                        output.append(f"📈 Confidence: {regime_analysis.get('confidence', 0):.1%}")
                
                elif response["function_called"] == "analyze_social_sentiment":
                    sentiment_analysis = function_result.get("analysis_summary", {})
                    if sentiment_analysis:
                        output.append(f"📱 Sentiment: {sentiment_analysis.get('overall_sentiment', 'unknown')}")
                        output.append(f"👥 Posts Analyzed: {sentiment_analysis.get('total_posts_analyzed', 0)}")
                
                elif response["function_called"] == "scan_ttm_squeeze_signals":
                    signals_found = function_result.get("signals_found", 0)
                    output.append(f"📈 TTM Signals Found: {signals_found}")
                
                elif response["function_called"] == "search_web":
                    results = function_result.get("total_results", 0)
                    output.append(f"🔍 Search Results: {results}")
                    
            else:
                output.append(f"❌ Function failed: {function_result.get('error', 'Unknown error')}")
        
        # Holly's response
        holly_response = response.get("response", "")
        if holly_response:
            output.append("\n🤖 Holly's Response:")
            output.append("-" * 50)
            output.append(holly_response)
        
        # Trading plan
        if response.get("trading_plan"):
            output.append("\n📋 Trading Plan Generated:")
            output.append("-" * 30)
            plan = response["trading_plan"]
            if isinstance(plan, dict):
                for key, value in plan.items():
                    output.append(f"{key}: {value}")
            else:
                output.append(str(plan))
        
        return "\n".join(output)
    
    async def process_command(self, user_input):
        """Process user command"""
        user_input = user_input.strip()
        
        # System commands
        if user_input.lower() in ['quit', 'exit']:
            self.running = False
            print("\n👋 Goodbye! Thanks for using Holly AI!")
            return
        
        elif user_input.lower() == 'help':
            self.print_help()
            return
        
        elif user_input.lower() == 'clear':
            os.system('cls' if os.name == 'nt' else 'clear')
            self.print_banner()
            return
        
        elif user_input.lower() == 'status':
            self.print_status()
            return
        
        elif user_input.lower() == 'config':
            self.print_config()
            return
        
        elif user_input.lower() == 'history':
            print("\n📜 Conversation History:")
            print("-" * 40)
            for i, msg in enumerate(self.conversation_history[-10:], 1):  # Last 10 messages
                timestamp = msg.get('timestamp', 'Unknown time')
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')[:100] + '...' if len(msg.get('content', '')) > 100 else msg.get('content', '')
                print(f"{i}. [{timestamp}] {role}: {content}")
            return
        
        # Empty input
        if not user_input:
            return
        
        # Process with Holly AI
        try:
            print("\n🤔 Holly is thinking...")
            
            # Record user message
            self.conversation_history.append({
                'role': 'user',
                'content': user_input,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })
            
            # Get Holly's response
            response = await self.holly.process_user_message(user_input)
            
            # Record Holly's response
            self.conversation_history.append({
                'role': 'assistant',
                'content': response.get('response', ''),
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'function_called': response.get('function_called'),
                'function_result': response.get('function_result')
            })
            
            # Format and display response
            formatted_response = self.format_response(response)
            print(f"\n{formatted_response}")
            
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'help' for available commands.")
    
    async def run(self):
        """Main CLI loop"""
        self.print_banner()
        
        while self.running:
            try:
                # Get user input
                user_input = input("\n💬 You: ").strip()
                
                # Process command
                await self.process_command(user_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using Holly AI!")
                break
            except EOFError:
                print("\n\n👋 Goodbye! Thanks for using Holly AI!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                print("Please try again or type 'help' for available commands.")


async def main():
    """Main entry point"""
    try:
        cli = HollyCLI()
        await cli.run()
    except Exception as e:
        print(f"Failed to start Holly AI CLI: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
