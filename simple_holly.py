#!/usr/bin/env python3
"""
Simple Holly AI interface - minimal version for testing
"""

import os
import asyncio
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(title="Holly AI Trading System", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class HollyRequest(BaseModel):
    message: str
    user_context: Optional[Dict[str, Any]] = None

class HollyResponse(BaseModel):
    response: str
    type: str = "chat"
    requires_action: bool = False
    timestamp: str

# Simple Holly AI Brain (minimal version)
class SimpleHollyBrain:
    def __init__(self):
        self.conversation_history = []
        
    async def process_message(self, message: str) -> Dict[str, Any]:
        """Process user message with simple responses"""
        from datetime import datetime
        
        # Add to conversation
        self.conversation_history.append({
            "role": "user", 
            "content": message,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Simple response logic
        message_lower = message.lower()
        
        if "make me" in message_lower and "$" in message_lower:
            response = """I understand you want to make money! Here's what I would do:

🎯 **AI-Enhanced Trading Plan**
1. **Scan for TTM Squeeze setups** - High-probability momentum plays
2. **Check AAPL, MSFT, GOOGL** - Blue chip stocks with good liquidity
3. **Use AI-enhanced stop losses** - Combining technical analysis + volatility + sentiment
4. **Apply 2% risk rule** - Never risk more than 2% of account per trade

📊 **Example Trade Setup with AI Stop Loss:**
- **Symbol:** AAPL (if showing momentum)
- **Entry:** $150.00
- **AI Stop:** $147.25 (calculated using support levels + volatility + news sentiment)
- **Target:** $155.50 (2:1 risk/reward ratio)
- **Risk:** $275 (1.8% of $15k position)

🧠 **AI Stop Loss Analysis:**
- Technical support at $147.50
- Current volatility: Normal (no adjustment needed)
- News sentiment: Neutral (no volatility increase)
- Final AI stop: $147.25 (slightly below support)

⚠️ **Important:** This is paper trading mode - perfect for learning without risk!

🚀 **Full Holly AI Features:**
In the complete system, I calculate AI-enhanced stops using:
- Real-time technical analysis (pivot points, S&R levels)
- Market volatility assessment (ATR, recent price action)
- News sentiment impact on volatility
- LLM-powered optimization of stop placement
- Multi-factor risk analysis

Would you like me to explain how AI-enhanced stop losses work?"""

        elif "stop loss" in message_lower or "stop" in message_lower:
            response = """🧠 **AI-Enhanced Stop Loss Calculation**

**Traditional Stop Loss:**
- Fixed percentage (e.g., 2% below entry)
- ATR-based (1.5x Average True Range)
- Round number levels

**Holly AI's Enhanced Approach:**
1. **Technical Analysis**
   - Identifies actual support/resistance levels
   - Analyzes pivot points and swing levels
   - Considers volume at key levels

2. **Volatility Assessment**
   - Current ATR vs historical average
   - Recent price action analysis
   - Market regime detection (trending vs ranging)

3. **News Sentiment Impact**
   - Analyzes recent news for volatility catalysts
   - Adjusts stops wider for negative sentiment
   - Tightens stops in stable news environment

4. **LLM Optimization**
   - ChatGPT analyzes all factors together
   - Provides reasoning for stop placement
   - Considers market structure and context

**Example Calculation:**
- Entry: $150.00
- Technical support: $147.50
- Current volatility: Normal
- News sentiment: Neutral
- **AI Final Stop: $147.25** (just below support with small buffer)

**Benefits:**
✅ More accurate than fixed percentages
✅ Adapts to market conditions
✅ Considers multiple data sources
✅ Provides clear reasoning
✅ Reduces false stop-outs

The full Holly AI system does this automatically for every trade!"""

        elif "aapl" in message_lower or "apple" in message_lower:
            response = """📈 **AAPL Analysis**

**Current Status:** Apple is a strong blue-chip stock with good liquidity for trading.

**What I'd look for:**
- TTM Squeeze setups on 15-min chart
- Volume confirmation on breakouts
- Support at major moving averages
- News sentiment (earnings, product launches)

**Trading Approach:**
- Wait for momentum confirmation
- Use tight stops below recent support
- Target previous resistance levels

💡 **Tip:** AAPL often moves with the broader tech sector (QQQ), so check overall market sentiment too!

🚀 **Full Holly AI Features:**
In the complete system, I can access 100+ API endpoints to get:
- Real-time quotes and technical indicators
- Company financials and analyst estimates
- Insider trading activity
- News sentiment analysis
- Earnings calendars and events
- Social sentiment data
- And much more!

Want me to explain TTM Squeeze strategy?"""

        elif "ttm" in message_lower or "squeeze" in message_lower:
            response = """🎯 **TTM Squeeze Strategy Explained**

**What is TTM Squeeze?**
- Bollinger Bands squeeze inside Keltner Channels
- Indicates low volatility before big moves
- When squeeze "fires" = momentum breakout

**How to Trade It:**
1. **Wait for squeeze to fire** (bands expand)
2. **Check momentum direction** (histogram color)
3. **Enter in direction of momentum**
4. **Stop below recent swing low/high**
5. **Target 2-3x risk for reward**

**Best Timeframes:**
- 15-minute for day trading
- 1-hour for swing trading
- Daily for position trading

**Success Rate:** ~65-70% when combined with volume confirmation

Want me to find some current TTM Squeeze setups?"""

        elif "hedge" in message_lower and "tesla" in message_lower:
            response = """🛡️ **Tesla Hedge Strategy**

**For Long TSLA Position:**

**Option 1: Sector Hedge**
- Short XLY (Consumer Discretionary ETF)
- Ratio: ~30% of TSLA position size
- Protects against sector-wide decline

**Option 2: Market Hedge**  
- Short SPY or QQQ
- Ratio: ~20% of TSLA position
- Protects against broad market decline

**Option 3: Correlated Stock**
- Short another high-beta tech stock
- Consider NVDA or AMD
- Similar volatility characteristics

**Recommendation:** Start with XLY hedge - it's simpler and effective for sector risk.

How many TSLA shares are you looking to hedge?"""

        elif any(word in message_lower for word in ["hello", "hi", "hey"]):
            response = """👋 **Hello! I'm Holly AI!**

I'm your AI trading assistant, ready to help you learn and trade! Here's what I can do:

🎯 **Trading Requests:**
- "Make me $50 today" - I'll create a trading plan
- "Find me momentum plays" - Scan for opportunities
- "What's AAPL looking like?" - Stock analysis

📊 **Market Analysis:**
- Real-time stock analysis
- TTM Squeeze setups
- Support/resistance levels
- News sentiment

🛡️ **Risk Management:**
- Position sizing calculations
- Stop loss placement
- Hedging strategies

💡 **Education:**
- Explain trading concepts
- Strategy breakdowns
- Market insights

**Try asking me:** "Make me $50 today" or "What's AAPL looking like?"

Remember: We're in **paper trading mode** - perfect for learning without risk! 📚"""

        else:
            response = f"""I understand you're asking about: "{message}"

I'm Holly AI, your trading assistant! While I'm running in simplified mode right now, I can still help you with:

🎯 **Try asking me:**
- "Make me $50 today"
- "What's AAPL looking like?"
- "Show me TTM Squeeze setups"
- "I need a hedge for Tesla"
- "Explain position sizing"

📚 **Learning Mode:**
This is paper trading - perfect for learning without risk!

💡 **Full Holly AI Features:**
- Real-time market data analysis
- AI-enhanced stop loss calculation  
- Complete trading plan generation
- Risk management tools

What would you like to learn about trading today?"""

        # Add response to conversation
        self.conversation_history.append({
            "role": "assistant",
            "content": response,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return {
            "response": response,
            "type": "chat",
            "requires_action": False
        }

# Global Holly instance
holly_brain = SimpleHollyBrain()

# API Routes
@app.post("/api/v1/holly/chat", response_model=HollyResponse)
async def chat_with_holly(request: HollyRequest):
    """Chat with Holly AI"""
    try:
        from datetime import datetime
        
        result = await holly_brain.process_message(request.message)
        
        return HollyResponse(
            response=result["response"],
            type=result["type"],
            requires_action=result["requires_action"],
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Holly encountered an error: {str(e)}")

@app.get("/api/v1/holly/capabilities")
async def get_capabilities():
    """Get Holly's capabilities"""
    return {
        "name": "Holly AI (Simplified Mode)",
        "description": "AI trading assistant - educational paper trading mode",
        "status": "Running in simplified mode",
        "capabilities": [
            "Natural language trading conversations",
            "Trading strategy explanations", 
            "Market analysis discussions",
            "Risk management education",
            "TTM Squeeze strategy teaching"
        ],
        "mode": "Educational/Paper Trading Only"
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Holly AI Trading System",
        "status": "Running in simplified mode",
        "chat_endpoint": "/api/v1/holly/chat",
        "docs": "/docs"
    }

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "mode": "simplified"}

if __name__ == "__main__":
    print("🤖 Starting Holly AI Trading System (Simplified Mode)")
    print("📚 Perfect for learning trading concepts!")
    print("")
    print("🌐 Access points:")
    print("  • API: http://localhost:8080")
    print("  • Docs: http://localhost:8080/docs")
    print("  • Chat: POST /api/v1/holly/chat")
    print("")
    print("💬 Try chatting with Holly:")
    print("  • 'Make me $50 today'")
    print("  • 'What's AAPL looking like?'")
    print("  • 'Explain TTM Squeeze'")
    print("")
    
    uvicorn.run(app, host="0.0.0.0", port=8080)
