"""
Social Sentiment Mining Service for Holly AI
Scrapes and analyzes sentiment from Twitter, StockTwits, Reddit, and other social platforms
"""

import asyncio
import aiohttp
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from src.core.config import settings
from src.core.logging import get_logger


class SentimentScore(Enum):
    """Sentiment classification"""
    VERY_BEARISH = -2
    BEARISH = -1
    NEUTRAL = 0
    BULLISH = 1
    VERY_BULLISH = 2


@dataclass
class SocialPost:
    """Represents a social media post"""
    platform: str
    symbol: str
    content: str
    author: str
    timestamp: datetime
    engagement: int  # likes, retweets, upvotes, etc.
    sentiment_score: float
    sentiment_label: SentimentScore
    confidence: float
    url: Optional[str] = None


class SocialSentimentService:
    """Service for mining and analyzing social sentiment around stocks"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # API configurations (add to settings as needed)
        self.twitter_bearer_token = getattr(settings, 'TWITTER_BEARER_TOKEN', None)
        self.reddit_client_id = getattr(settings, 'REDDIT_CLIENT_ID', None)
        self.reddit_client_secret = getattr(settings, 'REDDIT_CLIENT_SECRET', None)
        
        # Sentiment analysis cache
        self.sentiment_cache = {}
        
        # Common stock-related keywords for filtering
        self.stock_keywords = [
            'stock', 'shares', 'buy', 'sell', 'hold', 'bullish', 'bearish',
            'calls', 'puts', 'options', 'earnings', 'revenue', 'profit',
            'moon', 'rocket', 'diamond hands', 'paper hands', 'hodl',
            'squeeze', 'short', 'long', 'position', 'trade'
        ]
    
    async def get_symbol_sentiment(
        self, 
        symbol: str, 
        hours_back: int = 24,
        platforms: List[str] = None
    ) -> Dict[str, Any]:
        """
        Get aggregated sentiment for a symbol across platforms
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            hours_back: How many hours back to analyze
            platforms: List of platforms to check ['twitter', 'reddit', 'stocktwits']
        """
        if platforms is None:
            platforms = ['reddit', 'stocktwits']  # Start with free/accessible platforms
        
        try:
            all_posts = []
            platform_results = {}
            
            # Gather posts from each platform
            for platform in platforms:
                posts = await self._get_platform_posts(platform, symbol, hours_back)
                all_posts.extend(posts)
                platform_results[platform] = {
                    'post_count': len(posts),
                    'avg_sentiment': self._calculate_avg_sentiment(posts),
                    'engagement_weighted_sentiment': self._calculate_weighted_sentiment(posts)
                }
            
            # Analyze overall sentiment
            overall_analysis = self._analyze_overall_sentiment(all_posts)
            
            # Generate crowd mood score
            crowd_mood = self._calculate_crowd_mood(all_posts, platform_results)
            
            return {
                'symbol': symbol,
                'analysis_period_hours': hours_back,
                'total_posts': len(all_posts),
                'platforms_analyzed': platforms,
                'platform_breakdown': platform_results,
                'overall_sentiment': overall_analysis,
                'crowd_mood_score': crowd_mood,
                'timestamp': datetime.utcnow().isoformat(),
                'top_posts': self._get_top_posts(all_posts, limit=5),
                'sentiment_trend': self._calculate_sentiment_trend(all_posts),
                'trading_signals': self._extract_trading_signals(overall_analysis, crowd_mood)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sentiment for {symbol}: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'success': False
            }
    
    async def _get_platform_posts(self, platform: str, symbol: str, hours_back: int) -> List[SocialPost]:
        """Get posts from a specific platform"""
        try:
            if platform == 'reddit':
                return await self._get_reddit_posts(symbol, hours_back)
            elif platform == 'twitter':
                return await self._get_twitter_posts(symbol, hours_back)
            elif platform == 'stocktwits':
                return await self._get_stocktwits_posts(symbol, hours_back)
            else:
                self.logger.warning(f"Unknown platform: {platform}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting posts from {platform}: {e}")
            return []
    
    async def _get_reddit_posts(self, symbol: str, hours_back: int) -> List[SocialPost]:
        """Get posts from Reddit (using public API)"""
        posts = []
        
        try:
            # Search relevant subreddits
            subreddits = ['wallstreetbets', 'stocks', 'investing', 'SecurityAnalysis', 'StockMarket']
            
            async with aiohttp.ClientSession() as session:
                for subreddit in subreddits:
                    url = f"https://www.reddit.com/r/{subreddit}/search.json"
                    params = {
                        'q': f'${symbol} OR {symbol}',
                        'sort': 'new',
                        'restrict_sr': 'on',
                        't': 'day',  # Last day
                        'limit': 25
                    }
                    
                    headers = {'User-Agent': 'HollyAI/1.0'}
                    
                    async with session.get(url, params=params, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            for post_data in data.get('data', {}).get('children', []):
                                post = post_data.get('data', {})
                                
                                # Filter by time
                                post_time = datetime.fromtimestamp(post.get('created_utc', 0))
                                if post_time < datetime.utcnow() - timedelta(hours=hours_back):
                                    continue
                                
                                # Extract and analyze content
                                content = f"{post.get('title', '')} {post.get('selftext', '')}"
                                if self._is_relevant_content(content, symbol):
                                    sentiment_score, confidence = await self._analyze_sentiment(content)
                                    
                                    social_post = SocialPost(
                                        platform='reddit',
                                        symbol=symbol,
                                        content=content[:500],  # Truncate long posts
                                        author=post.get('author', 'unknown'),
                                        timestamp=post_time,
                                        engagement=post.get('score', 0) + post.get('num_comments', 0),
                                        sentiment_score=sentiment_score,
                                        sentiment_label=self._score_to_label(sentiment_score),
                                        confidence=confidence,
                                        url=f"https://reddit.com{post.get('permalink', '')}"
                                    )
                                    posts.append(social_post)
                        
                        # Rate limiting
                        await asyncio.sleep(0.5)
            
        except Exception as e:
            self.logger.error(f"Error fetching Reddit posts: {e}")
        
        return posts
    
    async def _get_stocktwits_posts(self, symbol: str, hours_back: int) -> List[SocialPost]:
        """Get posts from StockTwits (using public API)"""
        posts = []
        
        try:
            url = f"https://api.stocktwits.com/api/2/streams/symbol/{symbol}.json"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for message in data.get('messages', []):
                            # Filter by time
                            created_at = datetime.strptime(
                                message.get('created_at', ''), 
                                '%Y-%m-%dT%H:%M:%SZ'
                            )
                            if created_at < datetime.utcnow() - timedelta(hours=hours_back):
                                continue
                            
                            content = message.get('body', '')
                            sentiment_score, confidence = await self._analyze_sentiment(content)
                            
                            # StockTwits provides sentiment labels
                            st_sentiment = message.get('entities', {}).get('sentiment')
                            if st_sentiment:
                                if st_sentiment.get('basic') == 'Bullish':
                                    sentiment_score = max(sentiment_score, 0.5)
                                elif st_sentiment.get('basic') == 'Bearish':
                                    sentiment_score = min(sentiment_score, -0.5)
                            
                            social_post = SocialPost(
                                platform='stocktwits',
                                symbol=symbol,
                                content=content,
                                author=message.get('user', {}).get('username', 'unknown'),
                                timestamp=created_at,
                                engagement=message.get('likes', {}).get('total', 0),
                                sentiment_score=sentiment_score,
                                sentiment_label=self._score_to_label(sentiment_score),
                                confidence=confidence
                            )
                            posts.append(social_post)
                            
        except Exception as e:
            self.logger.error(f"Error fetching StockTwits posts: {e}")
        
        return posts
    
    async def _get_twitter_posts(self, symbol: str, hours_back: int) -> List[SocialPost]:
        """Get posts from Twitter (requires API key)"""
        posts = []
        
        if not self.twitter_bearer_token:
            self.logger.info("Twitter API not configured, skipping Twitter sentiment")
            return posts
        
        try:
            # Twitter API v2 search
            url = "https://api.twitter.com/2/tweets/search/recent"
            headers = {'Authorization': f'Bearer {self.twitter_bearer_token}'}
            
            query = f'${symbol} OR #{symbol} -is:retweet lang:en'
            params = {
                'query': query,
                'max_results': 100,
                'tweet.fields': 'created_at,public_metrics,author_id',
                'start_time': (datetime.utcnow() - timedelta(hours=hours_back)).isoformat()
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for tweet in data.get('data', []):
                            content = tweet.get('text', '')
                            sentiment_score, confidence = await self._analyze_sentiment(content)
                            
                            created_at = datetime.fromisoformat(
                                tweet.get('created_at', '').replace('Z', '+00:00')
                            )
                            
                            metrics = tweet.get('public_metrics', {})
                            engagement = (
                                metrics.get('like_count', 0) + 
                                metrics.get('retweet_count', 0) + 
                                metrics.get('reply_count', 0)
                            )
                            
                            social_post = SocialPost(
                                platform='twitter',
                                symbol=symbol,
                                content=content,
                                author=tweet.get('author_id', 'unknown'),
                                timestamp=created_at,
                                engagement=engagement,
                                sentiment_score=sentiment_score,
                                sentiment_label=self._score_to_label(sentiment_score),
                                confidence=confidence
                            )
                            posts.append(social_post)
                            
        except Exception as e:
            self.logger.error(f"Error fetching Twitter posts: {e}")
        
        return posts
    
    async def _analyze_sentiment(self, content: str) -> Tuple[float, float]:
        """
        Analyze sentiment of content using LLM
        Returns (sentiment_score, confidence) where score is -1 to 1
        """
        try:
            # Check cache first
            content_hash = hash(content)
            if content_hash in self.sentiment_cache:
                return self.sentiment_cache[content_hash]
            
            # Use OpenAI for sentiment analysis
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            
            prompt = f"""
            Analyze the sentiment of this social media post about stocks/trading.
            
            Post: "{content}"
            
            Return a JSON object with:
            - sentiment_score: number between -1 (very bearish) and 1 (very bullish)
            - confidence: number between 0 and 1 indicating confidence in the analysis
            - reasoning: brief explanation
            
            Consider trading-specific language like "moon", "diamond hands", "paper hands", etc.
            """
            
            response = await client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=150
            )
            
            result = json.loads(response.choices[0].message.content)
            sentiment_score = float(result.get('sentiment_score', 0))
            confidence = float(result.get('confidence', 0.5))
            
            # Cache the result
            self.sentiment_cache[content_hash] = (sentiment_score, confidence)
            
            return sentiment_score, confidence
            
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment: {e}")
            return 0.0, 0.0  # Neutral sentiment with low confidence
    
    def _is_relevant_content(self, content: str, symbol: str) -> bool:
        """Check if content is relevant to the symbol"""
        content_lower = content.lower()
        symbol_lower = symbol.lower()
        
        # Check for symbol mention
        if f'${symbol_lower}' in content_lower or symbol_lower in content_lower:
            return True
        
        # Check for stock-related keywords
        return any(keyword in content_lower for keyword in self.stock_keywords)
    
    def _score_to_label(self, score: float) -> SentimentScore:
        """Convert numeric score to sentiment label"""
        if score <= -0.6:
            return SentimentScore.VERY_BEARISH
        elif score <= -0.2:
            return SentimentScore.BEARISH
        elif score >= 0.6:
            return SentimentScore.VERY_BULLISH
        elif score >= 0.2:
            return SentimentScore.BULLISH
        else:
            return SentimentScore.NEUTRAL
    
    def _calculate_avg_sentiment(self, posts: List[SocialPost]) -> float:
        """Calculate average sentiment score"""
        if not posts:
            return 0.0
        return sum(post.sentiment_score for post in posts) / len(posts)
    
    def _calculate_weighted_sentiment(self, posts: List[SocialPost]) -> float:
        """Calculate engagement-weighted sentiment"""
        if not posts:
            return 0.0
        
        total_weighted_sentiment = sum(
            post.sentiment_score * (post.engagement + 1) * post.confidence 
            for post in posts
        )
        total_weight = sum((post.engagement + 1) * post.confidence for post in posts)
        
        return total_weighted_sentiment / total_weight if total_weight > 0 else 0.0
    
    def _analyze_overall_sentiment(self, posts: List[SocialPost]) -> Dict[str, Any]:
        """Analyze overall sentiment across all posts"""
        if not posts:
            return {'sentiment': 'neutral', 'confidence': 0.0}
        
        avg_sentiment = self._calculate_avg_sentiment(posts)
        weighted_sentiment = self._calculate_weighted_sentiment(posts)
        
        # Count sentiment distribution
        sentiment_counts = {}
        for post in posts:
            label = post.sentiment_label.name
            sentiment_counts[label] = sentiment_counts.get(label, 0) + 1
        
        # Determine overall sentiment
        if weighted_sentiment > 0.3:
            overall = 'bullish'
        elif weighted_sentiment < -0.3:
            overall = 'bearish'
        else:
            overall = 'neutral'
        
        return {
            'sentiment': overall,
            'avg_score': avg_sentiment,
            'weighted_score': weighted_sentiment,
            'confidence': sum(post.confidence for post in posts) / len(posts),
            'distribution': sentiment_counts,
            'total_posts': len(posts)
        }
    
    def _calculate_crowd_mood(self, posts: List[SocialPost], platform_results: Dict) -> Dict[str, Any]:
        """Calculate crowd mood score with additional insights"""
        if not posts:
            return {'score': 0.0, 'mood': 'neutral'}
        
        # Base mood from weighted sentiment
        weighted_sentiment = self._calculate_weighted_sentiment(posts)
        
        # Adjust for volume and engagement
        total_engagement = sum(post.engagement for post in posts)
        avg_engagement = total_engagement / len(posts) if posts else 0
        
        # Boost score if high engagement
        engagement_multiplier = min(1.5, 1 + (avg_engagement / 100))
        
        # Adjust for recency (more recent posts weighted higher)
        now = datetime.utcnow()
        recency_weights = []
        for post in posts:
            hours_ago = (now - post.timestamp).total_seconds() / 3600
            weight = max(0.1, 1 - (hours_ago / 24))  # Decay over 24 hours
            recency_weights.append(weight)
        
        if recency_weights:
            recency_adjusted_sentiment = sum(
                post.sentiment_score * weight 
                for post, weight in zip(posts, recency_weights)
            ) / sum(recency_weights)
        else:
            recency_adjusted_sentiment = weighted_sentiment
        
        # Final crowd mood score
        crowd_score = recency_adjusted_sentiment * engagement_multiplier
        crowd_score = max(-1.0, min(1.0, crowd_score))  # Clamp to [-1, 1]
        
        # Determine mood label
        if crowd_score > 0.4:
            mood = 'very_bullish'
        elif crowd_score > 0.1:
            mood = 'bullish'
        elif crowd_score < -0.4:
            mood = 'very_bearish'
        elif crowd_score < -0.1:
            mood = 'bearish'
        else:
            mood = 'neutral'
        
        return {
            'score': crowd_score,
            'mood': mood,
            'engagement_factor': engagement_multiplier,
            'recency_adjusted_sentiment': recency_adjusted_sentiment,
            'total_engagement': total_engagement,
            'avg_engagement': avg_engagement
        }
    
    def _get_top_posts(self, posts: List[SocialPost], limit: int = 5) -> List[Dict]:
        """Get top posts by engagement and sentiment strength"""
        if not posts:
            return []
        
        # Sort by engagement and sentiment strength
        sorted_posts = sorted(
            posts, 
            key=lambda p: (p.engagement * abs(p.sentiment_score) * p.confidence), 
            reverse=True
        )
        
        return [
            {
                'platform': post.platform,
                'content': post.content[:200] + '...' if len(post.content) > 200 else post.content,
                'sentiment_score': post.sentiment_score,
                'sentiment_label': post.sentiment_label.name,
                'engagement': post.engagement,
                'timestamp': post.timestamp.isoformat(),
                'url': post.url
            }
            for post in sorted_posts[:limit]
        ]
    
    def _calculate_sentiment_trend(self, posts: List[SocialPost]) -> Dict[str, Any]:
        """Calculate sentiment trend over time"""
        if len(posts) < 2:
            return {'trend': 'insufficient_data'}
        
        # Sort posts by time
        sorted_posts = sorted(posts, key=lambda p: p.timestamp)
        
        # Split into time buckets
        now = datetime.utcnow()
        recent_posts = [p for p in sorted_posts if (now - p.timestamp).total_seconds() < 3600]  # Last hour
        older_posts = [p for p in sorted_posts if (now - p.timestamp).total_seconds() >= 3600]
        
        if not recent_posts or not older_posts:
            return {'trend': 'insufficient_data'}
        
        recent_sentiment = self._calculate_avg_sentiment(recent_posts)
        older_sentiment = self._calculate_avg_sentiment(older_posts)
        
        trend_change = recent_sentiment - older_sentiment
        
        if trend_change > 0.2:
            trend = 'improving'
        elif trend_change < -0.2:
            trend = 'deteriorating'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'change': trend_change,
            'recent_sentiment': recent_sentiment,
            'older_sentiment': older_sentiment,
            'recent_posts_count': len(recent_posts),
            'older_posts_count': len(older_posts)
        }
    
    def _extract_trading_signals(self, overall_sentiment: Dict, crowd_mood: Dict) -> Dict[str, Any]:
        """Extract actionable trading signals from sentiment analysis"""
        signals = []
        
        mood_score = crowd_mood.get('score', 0)
        sentiment = overall_sentiment.get('sentiment', 'neutral')
        confidence = overall_sentiment.get('confidence', 0)
        
        # Strong bullish sentiment
        if mood_score > 0.5 and confidence > 0.7:
            signals.append({
                'type': 'bullish_sentiment',
                'strength': 'strong',
                'description': 'Very positive social sentiment with high confidence',
                'action': 'Consider long positions or call options'
            })
        
        # Strong bearish sentiment
        elif mood_score < -0.5 and confidence > 0.7:
            signals.append({
                'type': 'bearish_sentiment',
                'strength': 'strong',
                'description': 'Very negative social sentiment with high confidence',
                'action': 'Consider short positions or put options'
            })
        
        # Extreme sentiment (contrarian signal)
        if abs(mood_score) > 0.8:
            signals.append({
                'type': 'contrarian',
                'strength': 'moderate',
                'description': 'Extreme sentiment may indicate reversal opportunity',
                'action': 'Consider contrarian positions with tight stops'
            })
        
        return {
            'signals': signals,
            'overall_bias': sentiment,
            'confidence': confidence,
            'crowd_mood_score': mood_score
        }
