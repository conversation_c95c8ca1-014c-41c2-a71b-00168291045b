"""
<PERSON> Brain - The central LLM-powered trading orchestrator
This is the core intelligence that handles all trading decisions through ChatGPT
"""

import json
import logging
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
import openai
from openai import AsyncOpenAI

from src.core.config import settings
from src.core.logging import get_logger


class HollyAIBrain:
    """
    The central AI brain that orchestrates all trading decisions through ChatGPT.
    This is the primary interface - everything goes through the LLM.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
        self.temperature = 0.2  # Lower for more consistent trading decisions
        
        # Conversation context
        self.conversation_history: List[Dict] = []
        self.user_context: Dict[str, Any] = {}
        
        # Function registry - <PERSON>'s "skills"
        self.functions: Dict[str, Dict] = {}
        self.function_handlers: Dict[str, Any] = {}
        
        # Initialize <PERSON>'s personality and capabilities
        self._initialize_holly_personality()
        self._register_all_functions()
        
    def _initialize_holly_personality(self):
        """Initialize <PERSON>'s personality and trading expertise"""
        self.system_prompt = """You are <PERSON> AI, an expert quantitative trading assistant with deep knowledge of:

- TTM Squeeze setups and momentum trading
- Technical analysis (support/resistance, pivot points, volume analysis)
- Risk management and position sizing
- Market sentiment and news analysis
- Options strategies and hedging
- Portfolio management

Your personality:
- Professional but approachable, like talking to an experienced trader friend
- Always prioritize risk management over profit potential
- Explain complex concepts in simple terms for beginners
- Provide step-by-step reasoning for all trading decisions
- Never execute trades without proper stop losses and risk controls

Your capabilities:
- Interpret any trading request from natural language
- Access 100+ API endpoints for comprehensive market data
- Search the web for current news, earnings, events, and market information when internal data is insufficient
- Scan for TTM Squeeze breakout signals with AI-enhanced pattern recognition
- Analyze social media sentiment from Twitter, Reddit, and StockTwits for crowd mood scoring
- Detect market regimes using AI analysis of volatility, correlations, and market structure
- Generate advanced market context embeddings with time-series analysis and pattern matching
- Calculate AI-enhanced stop losses using advanced technical analysis, volatility assessment, and news sentiment
- Generate comprehensive trading plans with intelligent risk management
- Provide multi-source market analysis and sentiment assessment
- Create hedging strategies and position sizing recommendations
- Automatically optimize stop losses using LLM analysis of market structure

CRITICAL RULES:
1. ALWAYS use function calling for data access and calculations - never guess or hallucinate numbers
2. EVERY trade recommendation MUST include AI-enhanced stop loss calculation using calculate_ai_enhanced_stop_loss
3. Use comprehensive market analysis combining technical levels, volatility, and sentiment
4. When you need current news, earnings announcements, market events, or information not in your trading data, use search_web function
5. Explain your reasoning in plain English that a beginner can understand
6. When unsure about user intent, ask clarifying questions
7. All trading is in PAPER MODE - remind users this is for learning
8. Always prioritize risk management - proper stops are more important than profit targets

Remember: You're not just answering questions - you're actively managing trading decisions and creating executable plans."""

    def _register_all_functions(self):
        """Register all of Holly's trading functions"""
        
        # Core trading functions
        self._register_function(
            name="interpret_trading_goal",
            description="Parse any natural language trading request into structured parameters",
            parameters={
                "type": "object",
                "properties": {
                    "user_request": {"type": "string", "description": "The user's exact request"},
                    "profit_target": {"type": "number", "description": "Target profit in USD"},
                    "timeframe": {"type": "string", "enum": ["intraday", "swing", "position"], "description": "Trading timeframe"},
                    "risk_tolerance": {"type": "string", "enum": ["conservative", "moderate", "aggressive"]},
                    "hedging_requested": {"type": "boolean", "description": "Whether user wants hedging"},
                    "specific_symbols": {"type": "array", "items": {"type": "string"}, "description": "Any specific symbols mentioned"},
                    "strategy_preference": {"type": "string", "description": "Any strategy mentioned (momentum, mean reversion, etc.)"}
                },
                "required": ["user_request"]
            }
        )
        
        # Market data functions
        self._register_function(
            name="get_real_time_quote",
            description="Get current price, volume, and basic data for any symbol",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock symbol (e.g., AAPL, TSLA)"}
                },
                "required": ["symbol"]
            }
        )
        
        self._register_function(
            name="get_market_data_with_indicators",
            description="Get historical data with technical indicators for analysis",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string"},
                    "timeframe": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "1h", "1d"]},
                    "periods": {"type": "integer", "default": 100, "description": "Number of periods to analyze"}
                },
                "required": ["symbol", "timeframe"]
            }
        )
        
        # AI-enhanced analysis functions
        self._register_function(
            name="calculate_ai_enhanced_stop_loss",
            description="Calculate optimal stop-loss using AI analysis of technical levels, volatility, and market structure",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock symbol"},
                    "entry_price": {"type": "number", "description": "Planned entry price"},
                    "direction": {"type": "string", "enum": ["long", "short"], "description": "Trade direction"},
                    "timeframe": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "1h", "1d"], "description": "Trading timeframe"},
                    "risk_percent": {"type": "number", "default": 2.0, "description": "Maximum risk as percentage of account"},
                    "account_size": {"type": "number", "description": "Total account value for position sizing"},
                    "volatility_adjustment": {"type": "boolean", "default": True, "description": "Adjust stop based on current volatility"},
                    "news_sentiment_factor": {"type": "boolean", "default": True, "description": "Consider news sentiment in stop calculation"}
                },
                "required": ["symbol", "entry_price", "direction", "timeframe"]
            }
        )
        
        self._register_function(
            name="find_trading_opportunities",
            description="Scan the market for trading opportunities based on criteria",
            parameters={
                "type": "object",
                "properties": {
                    "strategy_type": {"type": "string", "enum": ["ttm_squeeze", "momentum_breakout", "mean_reversion", "any"]},
                    "min_confidence": {"type": "number", "default": 0.7},
                    "sectors": {"type": "array", "items": {"type": "string"}, "description": "Specific sectors to focus on"},
                    "max_price": {"type": "number", "description": "Maximum stock price"},
                    "min_volume": {"type": "number", "description": "Minimum average volume"}
                },
                "required": ["strategy_type"]
            }
        )
        
        # Advanced market analysis
        self._register_function(
            name="analyze_news_sentiment",
            description="Analyze news sentiment impact on specific symbols or market",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Symbol to analyze, or 'MARKET' for general sentiment"},
                    "time_range": {"type": "string", "enum": ["1h", "4h", "1d", "3d"], "default": "1d"}
                },
                "required": ["symbol"]
            }
        )
        
        self._register_function(
            name="get_fundamentals_analysis",
            description="Get fundamental analysis for investment decisions",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string"},
                    "analysis_type": {"type": "string", "enum": ["overview", "financials", "ratios", "insider_activity"]}
                },
                "required": ["symbol", "analysis_type"]
            }
        )
        
        # Portfolio and risk management
        self._register_function(
            name="calculate_position_size",
            description="Calculate optimal position size based on risk management",
            parameters={
                "type": "object",
                "properties": {
                    "entry_price": {"type": "number"},
                    "stop_price": {"type": "number"},
                    "risk_amount": {"type": "number", "description": "Dollar amount willing to risk"},
                    "account_size": {"type": "number", "description": "Total account value"}
                },
                "required": ["entry_price", "stop_price", "risk_amount"]
            }
        )
        
        self._register_function(
            name="create_hedge_strategy",
            description="Create hedging strategy for existing or planned positions",
            parameters={
                "type": "object",
                "properties": {
                    "primary_symbol": {"type": "string"},
                    "position_size": {"type": "number"},
                    "direction": {"type": "string", "enum": ["long", "short"]},
                    "hedge_type": {"type": "string", "enum": ["correlated_stock", "sector_etf", "market_hedge", "options"]}
                },
                "required": ["primary_symbol", "position_size", "direction"]
            }
        )
        
        # Execution functions
        self._register_function(
            name="create_trading_plan",
            description="Create a complete executable trading plan",
            parameters={
                "type": "object",
                "properties": {
                    "trades": {"type": "array", "items": {
                        "type": "object",
                        "properties": {
                            "symbol": {"type": "string"},
                            "action": {"type": "string", "enum": ["buy", "sell", "short", "cover"]},
                            "quantity": {"type": "number"},
                            "entry_price": {"type": "number"},
                            "stop_price": {"type": "number"},
                            "target_price": {"type": "number"},
                            "reasoning": {"type": "string"}
                        }
                    }},
                    "total_risk": {"type": "number"},
                    "expected_return": {"type": "number"},
                    "confidence_level": {"type": "number"}
                },
                "required": ["trades"]
            }
        )
        
        # Universal API access
        self._register_function(
            name="universal_api_access",
            description="Access ANY Alpaca or FMP endpoint - the ultimate data access function",
            parameters={
                "type": "object",
                "properties": {
                    "api": {"type": "string", "enum": ["alpaca", "fmp"], "description": "Which API to use"},
                    "endpoint_description": {"type": "string", "description": "What you want to get (e.g., 'account info', 'company profile', 'earnings calendar', 'insider trading')"},
                    "symbol": {"type": "string", "description": "Stock symbol if needed"},
                    "parameters": {"type": "object", "description": "Additional parameters"}
                },
                "required": ["api", "endpoint_description"]
            }
        )

        self._register_function(
            name="search_api_endpoints",
            description="Search available API endpoints by description to find what you need",
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "What you're looking for (e.g., 'earnings', 'insider trading', 'technical indicators')"}
                },
                "required": ["query"]
            }
        )

        self._register_function(
            name="get_comprehensive_stock_analysis",
            description="Get complete stock analysis using multiple data sources",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Stock symbol to analyze"}
                },
                "required": ["symbol"]
            }
        )

        # Legacy custom FMP access (kept for compatibility)
        self._register_function(
            name="custom_fmp_query",
            description="Access any Financial Modeling Prep endpoint for specialized data",
            parameters={
                "type": "object",
                "properties": {
                    "endpoint": {"type": "string", "description": "FMP API endpoint path"},
                    "parameters": {"type": "object", "description": "Query parameters"}
                },
                "required": ["endpoint"]
            }
        )

        # Web search for external information
        self._register_function(
            name="search_web",
            description="Search the web for current information when internal data is insufficient. Use for recent news, market events, company updates, earnings announcements, or any information not available in trading data.",
            parameters={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query - be specific and include relevant keywords like company name, ticker symbol, or specific topic"
                    },
                    "num_results": {
                        "type": "integer",
                        "description": "Number of search results to return (1-10, default 5)",
                        "minimum": 1,
                        "maximum": 10,
                        "default": 5
                    }
                },
                "required": ["query"]
            }
        )

        # Enhanced TTM Squeeze scanning
        self._register_function(
            name="scan_ttm_squeeze_signals",
            description="Scan for TTM Squeeze breakout signals with AI-enhanced pattern recognition. Detects squeeze conditions, histogram builds, and momentum confirmations.",
            parameters={
                "type": "object",
                "properties": {
                    "symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of symbols to scan (e.g., ['AAPL', 'TSLA', 'NVDA'])"
                    },
                    "timeframe": {
                        "type": "string",
                        "enum": ["1min", "5min", "15min", "30min", "1hour"],
                        "default": "5min",
                        "description": "Timeframe for analysis"
                    },
                    "max_results": {
                        "type": "integer",
                        "default": 10,
                        "description": "Maximum number of signals to return"
                    }
                },
                "required": ["symbols"]
            }
        )

        # Social sentiment analysis
        self._register_function(
            name="analyze_social_sentiment",
            description="Analyze social media sentiment for a stock from Twitter, Reddit, StockTwits. Provides crowd mood scoring and trading signals based on social sentiment.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol to analyze (e.g., 'AAPL', 'TSLA')"
                    },
                    "hours_back": {
                        "type": "integer",
                        "default": 24,
                        "description": "How many hours back to analyze (default 24)"
                    },
                    "platforms": {
                        "type": "array",
                        "items": {"type": "string", "enum": ["twitter", "reddit", "stocktwits"]},
                        "default": ["reddit", "stocktwits"],
                        "description": "Social platforms to analyze"
                    }
                },
                "required": ["symbol"]
            }
        )

        # Market regime detection
        self._register_function(
            name="detect_market_regime",
            description="Detect current market regime using AI analysis of volatility, correlations, and market structure. Provides strategy recommendations based on regime classification.",
            parameters={
                "type": "object",
                "properties": {
                    "symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "default": ["SPY", "QQQ", "IWM", "VIX"],
                        "description": "Symbols to analyze for regime detection (defaults to major indices)"
                    },
                    "include_symbol_analysis": {
                        "type": "boolean",
                        "default": False,
                        "description": "Include individual symbol regime analysis"
                    }
                },
                "required": []
            }
        )

        # Time-series embeddings analysis
        self._register_function(
            name="generate_market_embedding",
            description="Generate advanced market context embeddings using time-series analysis. Finds similar historical patterns and provides forecast indicators.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Primary symbol to analyze (e.g., 'AAPL', 'TSLA')"
                    },
                    "context_symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "default": ["SPY", "QQQ", "VIX", "TLT"],
                        "description": "Additional symbols for market context analysis"
                    }
                },
                "required": ["symbol"]
            }
        )
        
    def _register_function(self, name: str, description: str, parameters: Dict):
        """Register a function for Holly to use"""
        self.functions[name] = {
            "name": name,
            "description": description,
            "parameters": parameters
        }
        
    async def process_user_message(self, user_message: str, user_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Main entry point - process any user message and return Holly's response
        This is where all the magic happens
        """
        try:
            # Update user context
            if user_context:
                self.user_context.update(user_context)
                
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Prepare messages for ChatGPT
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (keep last 10 exchanges)
            recent_history = self.conversation_history[-20:]  # Last 10 exchanges
            for msg in recent_history:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
                
            # Call ChatGPT with function calling
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                functions=list(self.functions.values()),
                function_call="auto",
                temperature=self.temperature,
                max_tokens=2000
            )
            
            message = response.choices[0].message
            
            # Handle function calls
            if message.function_call:
                return await self._handle_function_call(message, messages)
            else:
                # Direct response
                holly_response = message.content
                
                self.conversation_history.append({
                    "role": "assistant",
                    "content": holly_response,
                    "timestamp": datetime.utcnow().isoformat()
                })
                
                return {
                    "response": holly_response,
                    "type": "chat",
                    "requires_action": False
                }
                
        except Exception as e:
            self.logger.error(f"Error processing user message: {e}")
            return {
                "response": "I encountered an error processing your request. Let me try a different approach - could you rephrase what you're looking for?",
                "type": "error",
                "error": str(e)
            }
            
    async def _handle_function_call(self, message, messages: List[Dict]) -> Dict[str, Any]:
        """Handle function calls and continue the conversation"""
        function_name = message.function_call.name
        function_args = json.loads(message.function_call.arguments)
        
        self.logger.info(f"Holly is calling function: {function_name} with args: {function_args}")
        
        # Execute the function
        function_result = await self._execute_function(function_name, function_args)
        
        # Add function call and result to conversation
        messages.append({
            "role": "assistant",
            "content": None,
            "function_call": {
                "name": function_name,
                "arguments": message.function_call.arguments
            }
        })
        
        messages.append({
            "role": "function",
            "name": function_name,
            "content": json.dumps(function_result, default=str)
        })
        
        # Get Holly's response with the function result
        final_response = await self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=self.temperature,
            max_tokens=2000
        )
        
        holly_response = final_response.choices[0].message.content
        
        # Add to conversation history
        self.conversation_history.append({
            "role": "assistant",
            "content": holly_response,
            "function_called": function_name,
            "function_result": function_result,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Determine response type and actions needed
        response_data = {
            "response": holly_response,
            "function_called": function_name,
            "function_result": function_result,
            "type": self._determine_response_type(function_name, function_result),
            "requires_action": self._requires_user_action(function_name, function_result)
        }
        
        # Add executable trading plan if created
        if function_name == "create_trading_plan" and function_result.get("success"):
            response_data["trading_plan"] = function_result["plan"]
            response_data["plan_id"] = function_result.get("plan_id")
            
        return response_data
        
    def _determine_response_type(self, function_name: str, result: Dict) -> str:
        """Determine the type of response for UI handling"""
        if function_name == "create_trading_plan":
            return "trading_plan"
        elif function_name in ["find_trading_opportunities", "get_market_data_with_indicators"]:
            return "market_analysis"
        elif function_name == "analyze_news_sentiment":
            return "sentiment_analysis"
        elif function_name in ["get_real_time_quote", "get_fundamentals_analysis"]:
            return "market_data"
        else:
            return "analysis"
            
    def _requires_user_action(self, function_name: str, result: Dict) -> bool:
        """Determine if user action is required"""
        return function_name == "create_trading_plan" and result.get("success", False)
        
    async def _execute_function(self, function_name: str, args: Dict) -> Dict[str, Any]:
        """Execute a function call - this is where we integrate with our trading services"""
        try:
            # Import services dynamically to avoid circular imports
            if function_name == "interpret_trading_goal":
                return await self._interpret_trading_goal(**args)
            elif function_name == "get_real_time_quote":
                return await self._get_real_time_quote(**args)
            elif function_name == "get_market_data_with_indicators":
                return await self._get_market_data_with_indicators(**args)
            elif function_name == "calculate_ai_stop_loss":
                return await self._calculate_ai_stop_loss(**args)
            elif function_name == "calculate_ai_enhanced_stop_loss":
                return await self._calculate_ai_enhanced_stop_loss(**args)
            elif function_name == "find_trading_opportunities":
                return await self._find_trading_opportunities(**args)
            elif function_name == "analyze_news_sentiment":
                return await self._analyze_news_sentiment(**args)
            elif function_name == "get_fundamentals_analysis":
                return await self._get_fundamentals_analysis(**args)
            elif function_name == "calculate_position_size":
                return await self._calculate_position_size(**args)
            elif function_name == "create_hedge_strategy":
                return await self._create_hedge_strategy(**args)
            elif function_name == "create_trading_plan":
                return await self._create_trading_plan(**args)
            elif function_name == "universal_api_access":
                return await self._universal_api_access(**args)
            elif function_name == "search_api_endpoints":
                return await self._search_api_endpoints(**args)
            elif function_name == "get_comprehensive_stock_analysis":
                return await self._get_comprehensive_stock_analysis(**args)
            elif function_name == "custom_fmp_query":
                return await self._custom_fmp_query(**args)
            elif function_name == "search_web":
                return await self._search_web(**args)
            elif function_name == "scan_ttm_squeeze_signals":
                return await self._scan_ttm_squeeze_signals(**args)
            elif function_name == "analyze_social_sentiment":
                return await self._analyze_social_sentiment(**args)
            elif function_name == "detect_market_regime":
                return await self._detect_market_regime(**args)
            elif function_name == "generate_market_embedding":
                return await self._generate_market_embedding(**args)
            else:
                return {"error": f"Unknown function: {function_name}"}
                
        except Exception as e:
            self.logger.error(f"Error executing function {function_name}: {e}")
            return {"error": str(e), "function": function_name}
            
    # Import function implementations
    async def _interpret_trading_goal(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.interpret_trading_goal(**kwargs)

    async def _get_real_time_quote(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.get_real_time_quote(**kwargs)

    async def _get_market_data_with_indicators(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.get_market_data_with_indicators(**kwargs)

    async def _calculate_ai_stop_loss(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.calculate_ai_stop_loss(**kwargs)

    async def _calculate_ai_enhanced_stop_loss(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.calculate_ai_enhanced_stop_loss(**kwargs)

    async def _find_trading_opportunities(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.find_trading_opportunities(**kwargs)

    async def _analyze_news_sentiment(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.analyze_news_sentiment(**kwargs)

    async def _get_fundamentals_analysis(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.get_fundamentals_analysis(**kwargs)

    async def _calculate_position_size(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.calculate_position_size(**kwargs)

    async def _create_hedge_strategy(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.create_hedge_strategy(**kwargs)

    async def _create_trading_plan(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.create_trading_plan(**kwargs)

    async def _universal_api_access(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.universal_api_access(**kwargs)

    async def _search_api_endpoints(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.search_api_endpoints(**kwargs)

    async def _get_comprehensive_stock_analysis(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.get_comprehensive_stock_analysis(**kwargs)

    async def _custom_fmp_query(self, **kwargs):
        from src.core.holly_functions import HollyFunctions
        functions = HollyFunctions()
        return await functions.custom_fmp_query(**kwargs)

    async def _search_web(self, **kwargs):
        """Search the web for current information when internal data is insufficient"""
        try:
            from src.services.web_search_service import WebSearchService

            search_service = WebSearchService()

            # Check if web search is properly configured
            if not search_service.is_configured():
                return {
                    "success": False,
                    "error": "Web search is not configured. Please set up Google Search API or Bing Search API keys.",
                    "available_providers": search_service.get_available_providers(),
                    "configuration_help": {
                        "google": "Set GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables",
                        "bing": "Set BING_SEARCH_API_KEY environment variable",
                        "note": "DuckDuckGo is available as fallback but provides limited results"
                    }
                }

            # Perform the search
            result = await search_service.search(
                query=kwargs.get("query"),
                num_results=kwargs.get("num_results", 5)
            )

            # Enhance the result with trading context if it's a financial search
            if result.get("success") and any(term in kwargs.get("query", "").lower()
                                           for term in ["stock", "earnings", "financial", "market", "trading", "price"]):
                result["trading_context"] = "This search appears to be financial/trading related. Consider using this information alongside technical analysis and risk management."

            return result

        except ImportError:
            return {
                "success": False,
                "error": "Web search service is not available. Please install required dependencies.",
                "fallback_suggestion": "Try using available market data functions or rephrase your query to use internal data sources."
            }
        except Exception as e:
            self.logger.error(f"Error in web search function: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestion": "Web search failed. Try rephrasing your query or using internal data sources."
            }

    async def _scan_ttm_squeeze_signals(self, **kwargs):
        """Scan for TTM Squeeze breakout signals with AI-enhanced pattern recognition"""
        try:
            from src.services.ttm_squeeze_scanner import TTMSqueezeScanner

            symbols = kwargs.get("symbols", [])
            timeframe = kwargs.get("timeframe", "5min")
            max_results = kwargs.get("max_results", 10)

            if not symbols:
                return {
                    "success": False,
                    "error": "No symbols provided for scanning",
                    "suggestion": "Provide a list of symbols to scan, e.g., ['AAPL', 'TSLA', 'NVDA']"
                }

            # Initialize scanner
            async with TTMSqueezeScanner() as scanner:
                # Scan symbols
                signals = await scanner.scan_multiple_symbols(symbols, timeframe)

                # Limit results
                signals = signals[:max_results]

                # Format results for LLM consumption
                formatted_signals = []
                for signal in signals:
                    formatted_signals.append({
                        "symbol": signal.symbol,
                        "direction": signal.side.value,
                        "entry_price": float(signal.entry_price),
                        "confidence": signal.confidence,
                        "timeframe": signal.timeframe,
                        "strategy": signal.strategy,
                        "metadata": signal.metadata,
                        "signal_strength": signal.metadata.get("pattern_analysis", {}).get("strength", 0),
                        "volume_ratio": signal.metadata.get("volume_ratio", 1.0),
                        "squeeze_conditions": signal.metadata.get("squeeze_conditions", {}),
                        "confirmation_score": signal.metadata.get("confirmation", {}).get("score", 0)
                    })

                return {
                    "success": True,
                    "signals_found": len(formatted_signals),
                    "symbols_scanned": len(symbols),
                    "timeframe": timeframe,
                    "signals": formatted_signals,
                    "summary": f"Found {len(formatted_signals)} TTM Squeeze signals from {len(symbols)} symbols scanned",
                    "next_steps": "Use calculate_ai_enhanced_stop_loss for each signal to determine optimal stop levels before trading"
                }

        except ImportError:
            return {
                "success": False,
                "error": "TTM Squeeze scanner is not available. Please check the implementation.",
                "fallback_suggestion": "Try using the basic find_trading_opportunities function instead."
            }
        except Exception as e:
            self.logger.error(f"Error in TTM Squeeze scanner: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestion": "Scanner failed. Try with fewer symbols or a different timeframe."
            }

    async def _analyze_social_sentiment(self, **kwargs):
        """Analyze social media sentiment for a stock"""
        try:
            from src.services.social_sentiment_service import SocialSentimentService

            symbol = kwargs.get("symbol")
            hours_back = kwargs.get("hours_back", 24)
            platforms = kwargs.get("platforms", ["reddit", "stocktwits"])

            if not symbol:
                return {
                    "success": False,
                    "error": "No symbol provided for sentiment analysis",
                    "suggestion": "Provide a stock symbol, e.g., 'AAPL'"
                }

            # Initialize sentiment service
            sentiment_service = SocialSentimentService()

            # Analyze sentiment
            result = await sentiment_service.get_symbol_sentiment(
                symbol=symbol.upper(),
                hours_back=hours_back,
                platforms=platforms
            )

            if result.get('error'):
                return {
                    "success": False,
                    "error": result.get('error'),
                    "symbol": symbol,
                    "fallback_suggestion": "Try with a different symbol or check social media API configuration"
                }

            # Format for LLM consumption
            formatted_result = {
                "success": True,
                "symbol": result.get('symbol'),
                "analysis_summary": {
                    "overall_sentiment": result.get('overall_sentiment', {}).get('sentiment', 'neutral'),
                    "crowd_mood": result.get('crowd_mood_score', {}).get('mood', 'neutral'),
                    "confidence": result.get('overall_sentiment', {}).get('confidence', 0),
                    "total_posts_analyzed": result.get('total_posts', 0),
                    "platforms_used": result.get('platforms_analyzed', [])
                },
                "detailed_analysis": {
                    "sentiment_scores": {
                        "average": result.get('overall_sentiment', {}).get('avg_score', 0),
                        "weighted": result.get('overall_sentiment', {}).get('weighted_score', 0),
                        "crowd_mood_score": result.get('crowd_mood_score', {}).get('score', 0)
                    },
                    "platform_breakdown": result.get('platform_breakdown', {}),
                    "sentiment_trend": result.get('sentiment_trend', {}),
                    "top_posts": result.get('top_posts', [])
                },
                "trading_signals": result.get('trading_signals', {}),
                "recommendations": self._generate_sentiment_recommendations(result),
                "timestamp": result.get('timestamp'),
                "analysis_period_hours": hours_back
            }

            return formatted_result

        except ImportError:
            return {
                "success": False,
                "error": "Social sentiment service is not available. Please check the implementation.",
                "fallback_suggestion": "Try using web search to find recent news about the symbol instead."
            }
        except Exception as e:
            self.logger.error(f"Error in social sentiment analysis: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestion": "Sentiment analysis failed. Try with a different symbol or check API configuration."
            }

    def _generate_sentiment_recommendations(self, sentiment_result: Dict) -> List[str]:
        """Generate trading recommendations based on sentiment analysis"""
        recommendations = []

        try:
            overall_sentiment = sentiment_result.get('overall_sentiment', {})
            crowd_mood = sentiment_result.get('crowd_mood_score', {})
            trading_signals = sentiment_result.get('trading_signals', {})

            sentiment = overall_sentiment.get('sentiment', 'neutral')
            mood_score = crowd_mood.get('score', 0)
            confidence = overall_sentiment.get('confidence', 0)

            # High confidence recommendations
            if confidence > 0.7:
                if sentiment == 'bullish' and mood_score > 0.3:
                    recommendations.append("Strong positive sentiment suggests potential upward momentum - consider long positions with proper risk management")
                elif sentiment == 'bearish' and mood_score < -0.3:
                    recommendations.append("Strong negative sentiment suggests potential downward pressure - consider defensive positioning")

            # Contrarian signals
            if abs(mood_score) > 0.8:
                recommendations.append("Extreme sentiment detected - consider contrarian positioning as sentiment may be overextended")

            # Volume and engagement insights
            total_posts = sentiment_result.get('total_posts', 0)
            if total_posts > 100:
                recommendations.append("High social media activity detected - increased volatility likely")
            elif total_posts < 10:
                recommendations.append("Low social media activity - sentiment may not be representative")

            # Trend analysis
            trend = sentiment_result.get('sentiment_trend', {}).get('trend')
            if trend == 'improving':
                recommendations.append("Sentiment is improving over time - momentum may be building")
            elif trend == 'deteriorating':
                recommendations.append("Sentiment is deteriorating - watch for potential reversal")

            # Default recommendation
            if not recommendations:
                recommendations.append("Sentiment analysis complete - use this information alongside technical analysis for trading decisions")

        except Exception as e:
            self.logger.error(f"Error generating sentiment recommendations: {e}")
            recommendations.append("Sentiment analysis completed - review results for trading insights")

        return recommendations

    async def _detect_market_regime(self, **kwargs):
        """Detect current market regime using AI analysis"""
        try:
            from src.services.regime_detection_service import VolatilityRegimeDetector

            symbols = kwargs.get("symbols", ["SPY", "QQQ", "IWM", "VIX"])
            include_symbol_analysis = kwargs.get("include_symbol_analysis", False)

            # Initialize regime detector
            async with VolatilityRegimeDetector() as detector:
                # Detect overall market regime
                regime_result = await detector.detect_current_regime(symbols)

                if not regime_result.get('success'):
                    return {
                        "success": False,
                        "error": regime_result.get('error', 'Regime detection failed'),
                        "fallback_suggestion": "Try with different symbols or check market data availability"
                    }

                # Add individual symbol analysis if requested
                symbol_analyses = {}
                if include_symbol_analysis:
                    for symbol in symbols[:3]:  # Limit to first 3 symbols
                        try:
                            symbol_regime = await detector.get_regime_for_symbol(symbol)
                            if symbol_regime.get('success'):
                                symbol_analyses[symbol] = {
                                    'regime': symbol_regime.get('current_regime'),
                                    'confidence': symbol_regime.get('confidence'),
                                    'insights': symbol_regime.get('symbol_specific_insights', [])
                                }
                        except Exception as e:
                            self.logger.error(f"Error analyzing regime for {symbol}: {e}")

                # Format result for LLM consumption
                formatted_result = {
                    "success": True,
                    "market_regime_analysis": {
                        "current_regime": regime_result.get('current_regime'),
                        "confidence": regime_result.get('confidence'),
                        "regime_duration_days": regime_result.get('regime_duration_days'),
                        "regime_change_probability": regime_result.get('regime_change_probability')
                    },
                    "regime_features": regime_result.get('features', {}),
                    "strategy_recommendations": regime_result.get('strategy_recommendations', []),
                    "risk_adjustment": regime_result.get('risk_adjustment', 1.0),
                    "regime_persistence": regime_result.get('regime_persistence', {}),
                    "symbols_analyzed": regime_result.get('symbols_analyzed', []),
                    "individual_symbol_analysis": symbol_analyses,
                    "trading_implications": self._generate_regime_trading_implications(regime_result),
                    "next_update_recommended": regime_result.get('next_update_recommended'),
                    "timestamp": regime_result.get('timestamp')
                }

                return formatted_result

        except ImportError:
            return {
                "success": False,
                "error": "Market regime detection service is not available. Please check the implementation.",
                "fallback_suggestion": "Try using basic market analysis functions instead."
            }
        except Exception as e:
            self.logger.error(f"Error in market regime detection: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestion": "Regime detection failed. Try with different symbols or check market conditions."
            }

    async def _generate_market_embedding(self, **kwargs):
        """Generate advanced market context embeddings"""
        try:
            from src.services.timeseries_embeddings_service import TimeSeriesEmbeddingsService

            symbol = kwargs.get("symbol")
            context_symbols = kwargs.get("context_symbols", ["SPY", "QQQ", "VIX", "TLT"])

            if not symbol:
                return {
                    "success": False,
                    "error": "No symbol provided for embedding analysis",
                    "suggestion": "Provide a symbol to analyze, e.g., 'AAPL'"
                }

            # Initialize embeddings service
            async with TimeSeriesEmbeddingsService() as embeddings_service:
                # Generate market embedding
                embedding_result = await embeddings_service.generate_market_embedding(
                    symbol=symbol.upper(),
                    context_symbols=context_symbols
                )

                if not embedding_result.get('success'):
                    return {
                        "success": False,
                        "error": embedding_result.get('error', 'Embedding generation failed'),
                        "symbol": symbol,
                        "fallback_suggestion": "Try with a different symbol or check data availability"
                    }

                # Format result for LLM consumption
                formatted_result = {
                    "success": True,
                    "symbol": embedding_result.get('symbol'),
                    "embedding_summary": {
                        "analysis_type": "time_series_embeddings",
                        "embedding_dimension": embedding_result.get('embedding_analysis', {}).get('embedding_dimension'),
                        "feature_count": embedding_result.get('embedding_analysis', {}).get('feature_count'),
                        "context_symbols": embedding_result.get('embedding_analysis', {}).get('context_symbols', [])
                    },
                    "market_context": embedding_result.get('market_context', {}),
                    "pattern_analysis": embedding_result.get('pattern_analysis', {}),
                    "trading_insights": embedding_result.get('trading_insights', []),
                    "forecast_indicators": embedding_result.get('forecast_indicators', {}),
                    "risk_assessment": embedding_result.get('risk_assessment', {}),
                    "recommendations": self._generate_embedding_recommendations(embedding_result),
                    "timestamp": embedding_result.get('embedding_analysis', {}).get('analysis_timestamp')
                }

                return formatted_result

        except ImportError:
            return {
                "success": False,
                "error": "Time-series embeddings service is not available. Please check the implementation.",
                "fallback_suggestion": "Try using basic technical analysis functions instead."
            }
        except Exception as e:
            self.logger.error(f"Error in market embedding generation: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_suggestion": "Embedding analysis failed. Try with a different symbol or check data availability."
            }

    def _generate_regime_trading_implications(self, regime_result: Dict) -> List[str]:
        """Generate trading implications from regime analysis"""
        implications = []

        try:
            regime = regime_result.get('current_regime', 'calm')
            confidence = regime_result.get('confidence', 0.5)
            risk_adjustment = regime_result.get('risk_adjustment', 1.0)

            # High confidence implications
            if confidence > 0.7:
                if regime == 'trending':
                    implications.append("Strong trending regime detected - momentum strategies favored")
                    implications.append("Consider breakout trades and trend-following approaches")
                elif regime == 'mean_reverting':
                    implications.append("Mean-reverting regime identified - look for oversold/overbought opportunities")
                    implications.append("Range trading and contrarian strategies may be effective")
                elif regime == 'turbulent':
                    implications.append("Turbulent market conditions - reduce position sizes and use wider stops")
                    implications.append("Volatility trading strategies may be profitable")
                elif regime == 'crisis':
                    implications.append("Crisis regime detected - prioritize capital preservation")
                    implications.append("Consider defensive positioning and hedging strategies")

            # Risk adjustment implications
            if risk_adjustment < 0.7:
                implications.append(f"Risk adjustment factor: {risk_adjustment:.1f} - reduce normal position sizes")
            elif risk_adjustment > 1.2:
                implications.append(f"Risk adjustment factor: {risk_adjustment:.1f} - potential for increased exposure")

            # Regime change implications
            change_prob = regime_result.get('regime_change_probability', 0.2)
            if change_prob > 0.6:
                implications.append("High probability of regime change - monitor for transition signals")

            if not implications:
                implications.append("Regime analysis complete - integrate with technical analysis for trading decisions")

        except Exception as e:
            self.logger.error(f"Error generating regime implications: {e}")
            implications.append("Regime analysis completed - review results for market context")

        return implications

    def _generate_embedding_recommendations(self, embedding_result: Dict) -> List[str]:
        """Generate recommendations from embedding analysis"""
        recommendations = []

        try:
            # Pattern-based recommendations
            pattern_analysis = embedding_result.get('pattern_analysis', {})
            pattern_confidence = pattern_analysis.get('pattern_confidence', 0.0)

            if pattern_confidence > 0.7:
                recommendations.append("Strong historical pattern matches found - high confidence in analysis")

                # Check forecast indicators
                forecast = embedding_result.get('forecast_indicators', {})
                expected_return = forecast.get('expected_5day_return', 0)

                if expected_return > 0.02:
                    recommendations.append("Historical patterns suggest positive momentum over next 5 days")
                elif expected_return < -0.02:
                    recommendations.append("Historical patterns suggest potential downward pressure")

            # Risk-based recommendations
            risk_assessment = embedding_result.get('risk_assessment', {})
            risk_level = risk_assessment.get('risk_level', 'medium')

            if risk_level == 'high':
                recommendations.append("High risk environment detected - use conservative position sizing")
            elif risk_level == 'low':
                recommendations.append("Low risk environment - potential for increased exposure")

            # Market context recommendations
            market_context = embedding_result.get('market_context', {})
            stress_level = market_context.get('market_stress_level', 0.5)

            if stress_level > 0.8:
                recommendations.append("High market stress - consider defensive strategies")
            elif stress_level < 0.3:
                recommendations.append("Low market stress - favorable environment for risk-taking")

            # Trading insights
            trading_insights = embedding_result.get('trading_insights', [])
            if trading_insights:
                recommendations.extend(trading_insights[:2])  # Add top 2 insights

            if not recommendations:
                recommendations.append("Embedding analysis complete - use insights alongside technical analysis")

        except Exception as e:
            self.logger.error(f"Error generating embedding recommendations: {e}")
            recommendations.append("Embedding analysis completed - review results for trading context")

        return recommendations
