import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  AppBar,
  Toolbar,
  Card,
  CardContent,
  Tabs,
  Tab
} from '@mui/material';
import HollyChat from './components/HollyChat';
import Dashboard from './components/Dashboard';
import SignalsList from './components/SignalsList';
import PositionsList from './components/PositionsList';
import AccountInfo from './components/AccountInfo';
import AIFeatures from './components/AIFeatures';
import './App.css';

function App() {
  const [accountData, setAccountData] = useState(null);
  const [positions, setPositions] = useState([]);
  const [signals, setSignals] = useState([]);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    // Fetch initial data
    fetchAccountData();
    fetchPositions();
    fetchSignals();

    // Set up periodic updates
    const interval = setInterval(() => {
      fetchAccountData();
      fetchPositions();
      fetchSignals();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchAccountData = async () => {
    try {
      const response = await fetch('/api/v1/account');
      if (response.ok) {
        const data = await response.json();
        setAccountData(data);
      }
    } catch (error) {
      console.error('Error fetching account data:', error);
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/v1/positions');
      if (response.ok) {
        const data = await response.json();
        setPositions(data);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
    }
  };

  const fetchSignals = async () => {
    try {
      const response = await fetch('/api/v1/signals');
      if (response.ok) {
        const data = await response.json();
        setSignals(data);
      }
    } catch (error) {
      console.error('Error fetching signals:', error);
    }
  };

  return (
    <div className="App">
      <AppBar position="static" sx={{ backgroundColor: '#1976d2' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            🤖 Holly AI Trading System
          </Typography>
          <Typography variant="body2" sx={{
            backgroundColor: 'rgba(255,255,255,0.2)',
            px: 1,
            py: 0.5,
            borderRadius: 1
          }}>
            Paper Trading Mode
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 2 }}>
        {/* Top Row - Account Info and Dashboard */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <AccountInfo accountData={accountData} />
          </Grid>
          <Grid item xs={12} md={6}>
            <Dashboard
              accountData={accountData}
              positions={positions}
              signals={signals}
            />
          </Grid>
        </Grid>

        {/* Main Tabbed Interface */}
        <Paper sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={activeTab}
              onChange={(event, newValue) => setActiveTab(newValue)}
              variant="fullWidth"
              sx={{
                '& .MuiTab-root': {
                  fontSize: '1rem',
                  fontWeight: 600,
                  textTransform: 'none'
                }
              }}
            >
              <Tab label="🤖 Holly AI Chat" />
              <Tab label="🧠 AI Features" />
              <Tab label="📊 Signals & Positions" />
            </Tabs>
          </Box>

          {/* Tab Content */}
          <Box sx={{ p: 3 }}>
            {activeTab === 0 && (
              <HollyChat onPlanExecuted={() => {
                fetchAccountData();
                fetchPositions();
                fetchSignals();
              }} />
            )}

            {activeTab === 1 && (
              <AIFeatures />
            )}

            {activeTab === 2 && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <SignalsList signals={signals} />
                </Grid>
                <Grid item xs={12} md={6}>
                  <PositionsList positions={positions} />
                </Grid>
              </Grid>
            )}
          </Box>
        </Paper>
      </Container>
    </div>
  );
}

export default App;
