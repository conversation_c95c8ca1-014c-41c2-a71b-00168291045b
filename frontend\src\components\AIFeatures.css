.ai-features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  color: white;
}

.ai-header {
  text-align: center;
  margin-bottom: 30px;
}

.ai-header h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.ai-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 10px;
}

.tab-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.ai-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-tab-content h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  color: #fff;
}

.ai-tab-content p {
  margin-bottom: 25px;
  opacity: 0.9;
  font-size: 1.1rem;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #fff;
}

.input-group input,
.input-group select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.input-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-group input:focus,
.input-group select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.ai-button {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  margin-top: 10px;
}

.ai-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.ai-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.results-section {
  margin-top: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.results-section h4 {
  color: #fff;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.regime-summary,
.sentiment-summary,
.embeddings-summary,
.ttm-summary,
.news-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.regime-item,
.sentiment-item,
.embedding-item,
.ttm-item,
.news-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.regime-item strong,
.sentiment-item strong,
.embedding-item strong,
.ttm-item strong,
.news-item strong {
  display: block;
  margin-bottom: 5px;
  color: #fff;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.holly-interpretation {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
  border-left: 4px solid #4CAF50;
}

.holly-interpretation h5 {
  color: #4CAF50;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.holly-interpretation p {
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.error-message {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.4);
  color: #fff;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-features-container {
    padding: 15px;
    margin: 10px;
  }
  
  .ai-header h2 {
    font-size: 2rem;
  }
  
  .ai-tabs {
    flex-direction: column;
    align-items: center;
  }
  
  .tab-button {
    width: 200px;
    text-align: center;
  }
  
  .ai-content {
    padding: 20px;
  }
  
  .regime-summary,
  .sentiment-summary,
  .embeddings-summary,
  .ttm-summary,
  .news-summary {
    grid-template-columns: 1fr;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.ai-button:disabled {
  animation: pulse 1.5s infinite;
}

/* Hover effects for result items */
.regime-item:hover,
.sentiment-item:hover,
.embedding-item:hover,
.ttm-item:hover,
.news-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Gradient text for values */
.regime-item:not(strong),
.sentiment-item:not(strong),
.embedding-item:not(strong),
.ttm-item:not(strong),
.news-item:not(strong) {
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  font-size: 1.1rem;
}
