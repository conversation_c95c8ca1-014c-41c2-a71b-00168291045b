#!/usr/bin/env python3
"""
Test script for Holly AI web search functionality
Run this to test web search integration before using it in production
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain
from src.services.web_search_service import WebSearchService


async def test_web_search_service():
    """Test the WebSearchService directly"""
    print("🔍 Testing WebSearchService directly...")
    
    search_service = WebSearchService()
    
    # Check configuration
    print(f"Available providers: {search_service.get_available_providers()}")
    print(f"Is configured: {search_service.is_configured()}")
    print(f"Primary provider: {search_service.primary_provider}")
    
    # Test search
    test_queries = [
        "AAPL earnings Q4 2024",
        "Tesla stock news today",
        "Federal Reserve interest rate decision"
    ]
    
    for query in test_queries:
        print(f"\n📊 Searching for: '{query}'")
        result = await search_service.search(query, num_results=3)
        
        if result.get("success"):
            print(f"✅ Found {result.get('total_results', 0)} results")
            for i, item in enumerate(result.get("results", [])[:2], 1):
                print(f"  {i}. {item.get('title', 'No title')[:80]}...")
                print(f"     {item.get('snippet', 'No snippet')[:100]}...")
        else:
            print(f"❌ Search failed: {result.get('error', 'Unknown error')}")
            if result.get("fallback_suggestion"):
                print(f"💡 Suggestion: {result.get('fallback_suggestion')}")


async def test_holly_web_search():
    """Test web search through Holly AI Brain"""
    print("\n🤖 Testing web search through Holly AI...")
    
    holly = HollyAIBrain()
    
    # Test queries that should trigger web search
    test_messages = [
        "What's the latest news about Apple earnings?",
        "Search for recent Tesla stock news",
        "Find information about the latest Federal Reserve meeting",
        "What are analysts saying about NVIDIA's recent performance?"
    ]
    
    for message in test_messages:
        print(f"\n💬 User: {message}")
        
        try:
            response = await holly.process_user_message(message)
            
            if response.get("function_called") == "search_web":
                print("✅ Holly used web search function")
                function_result = response.get("function_result", {})
                
                if function_result.get("success"):
                    results = function_result.get("results", [])
                    print(f"📊 Found {len(results)} results")
                    if results:
                        print(f"🔍 Top result: {results[0].get('title', 'No title')}")
                else:
                    print(f"❌ Search failed: {function_result.get('error', 'Unknown error')}")
            else:
                print(f"ℹ️  Holly used function: {response.get('function_called', 'none')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:200]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_configuration_scenarios():
    """Test different configuration scenarios"""
    print("\n⚙️  Testing configuration scenarios...")
    
    # Save original env vars
    original_google_key = os.environ.get("GOOGLE_SEARCH_API_KEY")
    original_google_id = os.environ.get("GOOGLE_SEARCH_ENGINE_ID")
    original_bing_key = os.environ.get("BING_SEARCH_API_KEY")
    
    try:
        # Test 1: No API keys configured
        print("\n1️⃣  Testing with no API keys...")
        for key in ["GOOGLE_SEARCH_API_KEY", "GOOGLE_SEARCH_ENGINE_ID", "BING_SEARCH_API_KEY"]:
            if key in os.environ:
                del os.environ[key]
        
        search_service = WebSearchService()
        print(f"Available providers: {search_service.get_available_providers()}")
        print(f"Primary provider: {search_service.primary_provider}")
        
        result = await search_service.search("test query", num_results=2)
        print(f"Search result: {'Success' if result.get('success') else 'Failed'}")
        
        # Test 2: With Google API keys (if available)
        if original_google_key and original_google_id:
            print("\n2️⃣  Testing with Google Search API...")
            os.environ["GOOGLE_SEARCH_API_KEY"] = original_google_key
            os.environ["GOOGLE_SEARCH_ENGINE_ID"] = original_google_id
            
            search_service = WebSearchService()
            print(f"Primary provider: {search_service.primary_provider}")
            
            result = await search_service.search("AAPL stock", num_results=2)
            print(f"Search result: {'Success' if result.get('success') else 'Failed'}")
        
        # Test 3: With Bing API key (if available)
        if original_bing_key:
            print("\n3️⃣  Testing with Bing Search API...")
            # Clear Google keys
            for key in ["GOOGLE_SEARCH_API_KEY", "GOOGLE_SEARCH_ENGINE_ID"]:
                if key in os.environ:
                    del os.environ[key]
            
            os.environ["BING_SEARCH_API_KEY"] = original_bing_key
            
            search_service = WebSearchService()
            print(f"Primary provider: {search_service.primary_provider}")
            
            result = await search_service.search("TSLA news", num_results=2)
            print(f"Search result: {'Success' if result.get('success') else 'Failed'}")
    
    finally:
        # Restore original environment
        if original_google_key:
            os.environ["GOOGLE_SEARCH_API_KEY"] = original_google_key
        if original_google_id:
            os.environ["GOOGLE_SEARCH_ENGINE_ID"] = original_google_id
        if original_bing_key:
            os.environ["BING_SEARCH_API_KEY"] = original_bing_key


async def main():
    """Run all tests"""
    print("🚀 Holly AI Web Search Integration Test")
    print("=" * 50)
    
    try:
        await test_web_search_service()
        await test_configuration_scenarios()
        await test_holly_web_search()
        
        print("\n✅ All tests completed!")
        print("\n💡 Setup Tips:")
        print("1. For best results, configure Google Custom Search API")
        print("2. Bing Search API is a good alternative")
        print("3. DuckDuckGo works as fallback but has limited results")
        print("4. Add API keys to your .env file")
        print("5. Holly will automatically use web search when needed")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
