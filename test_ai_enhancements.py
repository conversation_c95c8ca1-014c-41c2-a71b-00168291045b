#!/usr/bin/env python3
"""
Test script for Holly AI's enhanced capabilities
Tests web search, TTM Squeeze scanning, and social sentiment analysis
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.holly_ai_brain import HollyAIBrain


async def test_web_search_integration():
    """Test web search functionality through Holly"""
    print("🔍 Testing Web Search Integration...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "Search for the latest Apple earnings news",
        "Find recent information about Tesla stock performance",
        "What's the latest news about the Federal Reserve interest rates?"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "search_web":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    results = function_result.get("results", [])
                    print(f"✅ Found {len(results)} web results")
                    if results:
                        print(f"🔍 Top result: {results[0].get('title', 'No title')[:80]}...")
                else:
                    print(f"❌ Search failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:150]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_ttm_squeeze_scanner():
    """Test TTM Squeeze scanning functionality"""
    print("\n📊 Testing TTM Squeeze Scanner...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "Scan AAPL, TSLA, NVDA for TTM Squeeze signals",
        "Look for TTM Squeeze breakouts in SPY, QQQ, IWM on 15min timeframe",
        "Find TTM Squeeze signals in MSFT and GOOGL"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "scan_ttm_squeeze_signals":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    signals = function_result.get("signals", [])
                    print(f"✅ Found {len(signals)} TTM Squeeze signals")
                    for signal in signals[:3]:  # Show first 3
                        print(f"📈 {signal.get('symbol')}: {signal.get('direction')} at ${signal.get('entry_price')} (confidence: {signal.get('confidence'):.2f})")
                else:
                    print(f"❌ Scan failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:150]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_social_sentiment_analysis():
    """Test social sentiment analysis functionality"""
    print("\n📱 Testing Social Sentiment Analysis...")
    
    holly = HollyAIBrain()
    
    test_queries = [
        "Analyze social sentiment for AAPL",
        "What's the crowd mood for Tesla on social media?",
        "Check Reddit and StockTwits sentiment for NVDA"
    ]
    
    for query in test_queries:
        print(f"\n💬 User: {query}")
        
        try:
            response = await holly.process_user_message(query)
            
            if response.get("function_called") == "analyze_social_sentiment":
                function_result = response.get("function_result", {})
                if function_result.get("success"):
                    analysis = function_result.get("analysis_summary", {})
                    print(f"✅ Sentiment Analysis Complete:")
                    print(f"   Overall Sentiment: {analysis.get('overall_sentiment', 'unknown')}")
                    print(f"   Crowd Mood: {analysis.get('crowd_mood', 'unknown')}")
                    print(f"   Posts Analyzed: {analysis.get('total_posts_analyzed', 0)}")
                    print(f"   Confidence: {analysis.get('confidence', 0):.2f}")
                else:
                    print(f"❌ Analysis failed: {function_result.get('error', 'Unknown error')}")
            
            # Show Holly's response (truncated)
            holly_response = response.get("response", "No response")
            print(f"🤖 Holly: {holly_response[:150]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_integrated_workflow():
    """Test integrated workflow combining multiple AI capabilities"""
    print("\n🚀 Testing Integrated AI Workflow...")
    
    holly = HollyAIBrain()
    
    # Complex query that should trigger multiple functions
    complex_query = """
    I want to make $100 today. First check the social sentiment for AAPL and TSLA, 
    then scan for TTM Squeeze signals, and search for any recent news that might 
    affect these stocks. Create a comprehensive trading plan.
    """
    
    print(f"💬 User: {complex_query}")
    
    try:
        response = await holly.process_user_message(complex_query)
        
        function_called = response.get("function_called")
        print(f"🔧 Holly used function: {function_called}")
        
        if function_called:
            function_result = response.get("function_result", {})
            if function_result.get("success"):
                print("✅ Function executed successfully")
            else:
                print(f"❌ Function failed: {function_result.get('error', 'Unknown error')}")
        
        # Show Holly's full response
        holly_response = response.get("response", "No response")
        print(f"\n🤖 Holly's Response:")
        print(holly_response)
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def test_ai_enhanced_stop_loss():
    """Test AI-enhanced stop loss calculation"""
    print("\n🛡️ Testing AI-Enhanced Stop Loss...")
    
    holly = HollyAIBrain()
    
    test_query = "Calculate an AI-enhanced stop loss for AAPL at entry price $150, going long on 5min timeframe"
    
    print(f"💬 User: {test_query}")
    
    try:
        response = await holly.process_user_message(test_query)
        
        function_called = response.get("function_called")
        print(f"🔧 Holly used function: {function_called}")
        
        if function_called == "calculate_ai_enhanced_stop_loss":
            function_result = response.get("function_result", {})
            if function_result.get("success"):
                stop_data = function_result.get("stop_analysis", {})
                print(f"✅ AI Stop Loss Calculated:")
                print(f"   Stop Price: ${stop_data.get('recommended_stop', 'N/A')}")
                print(f"   Risk Amount: ${stop_data.get('risk_amount', 'N/A')}")
                print(f"   Confidence: {stop_data.get('confidence', 0):.2f}")
            else:
                print(f"❌ Calculation failed: {function_result.get('error', 'Unknown error')}")
        
        # Show Holly's response (truncated)
        holly_response = response.get("response", "No response")
        print(f"🤖 Holly: {holly_response[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def main():
    """Run all AI enhancement tests"""
    print("🎯 Holly AI Enhanced Capabilities Test Suite")
    print("=" * 60)
    
    try:
        # Test individual capabilities
        await test_web_search_integration()
        await test_ttm_squeeze_scanner()
        await test_social_sentiment_analysis()
        await test_ai_enhanced_stop_loss()
        
        # Test integrated workflow
        await test_integrated_workflow()
        
        print("\n✅ All tests completed!")
        print("\n🎉 Holly AI Enhanced Capabilities Summary:")
        print("1. ✅ Web Search Integration - Find current news and events")
        print("2. ✅ TTM Squeeze Scanner - AI-enhanced pattern recognition")
        print("3. ✅ Social Sentiment Analysis - Crowd mood from social media")
        print("4. ✅ AI-Enhanced Stop Loss - Advanced risk management")
        print("5. ✅ Integrated Workflow - Multiple AI functions working together")
        
        print("\n💡 Next Steps:")
        print("- Configure API keys for enhanced functionality")
        print("- Test with real market data")
        print("- Integrate with your trading workflow")
        print("- Monitor performance and adjust parameters")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
