"""
Enhanced TTM Squeeze Scanner for Holly AI
Integrates with the existing signal detection system and adds AI-enhanced pattern recognition
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal

from src.core.config import settings
from src.core.logging import get_logger
from src.services.fmp_service import FMPService
from src.models.trading import Signal, OrderSide


class TTMSqueezeScanner:
    """Enhanced TTM Squeeze scanner with AI integration"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.fmp_service = None
        
        # TTM Squeeze parameters (configurable)
        self.bb_period = getattr(settings, 'TTM_BB_PERIOD', 20)
        self.bb_stddev = getattr(settings, 'TTM_BB_STDDEV', 2.0)
        self.kc_period = getattr(settings, 'TTM_KC_PERIOD', 20)
        self.kc_multiplier = getattr(settings, 'TTM_KC_MULTIPLIER', 1.5)
        
        # Enhanced parameters
        self.hist_lookback = 20
        self.momentum_lookback = 4
        self.min_volume_threshold = 100000  # Minimum daily volume
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.fmp_service = FMPService()
        await self.fmp_service.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.fmp_service:
            await self.fmp_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def fetch_intraday_data(self, symbol: str, timeframe: str = "5min", limit: int = 500) -> pd.DataFrame:
        """
        Fetch intraday data for TTM analysis
        
        Args:
            symbol: Stock symbol
            timeframe: Data timeframe (1min, 5min, 15min, 30min, 1hour)
            limit: Number of bars to fetch
        """
        try:
            # Use FMP historical chart endpoint
            endpoint = f"v3/historical-chart/{timeframe}/{symbol}"
            params = {"limit": limit}
            
            response = await self.fmp_service.get_data(endpoint, params)
            
            if not response or not isinstance(response, list):
                self.logger.warning(f"No intraday data for {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(response)
            
            if df.empty:
                return df
                
            # Clean and prepare data
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            
            # Ensure numeric columns
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Filter out zero volume bars
            df = df[df['volume'] > 0]
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching intraday data for {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_ttm_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate TTM Squeeze indicators with enhanced features
        """
        if df.empty or len(df) < max(self.bb_period, self.kc_period, self.hist_lookback):
            return df
            
        try:
            # Bollinger Bands
            df['sma'] = df['close'].rolling(window=self.bb_period).mean()
            df['bb_std'] = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['sma'] + (self.bb_stddev * df['bb_std'])
            df['bb_lower'] = df['sma'] - (self.bb_stddev * df['bb_std'])
            
            # True Range and ATR
            df['prev_close'] = df['close'].shift(1)
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = (df['high'] - df['prev_close']).abs()
            df['tr3'] = (df['low'] - df['prev_close']).abs()
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            df['atr'] = df['true_range'].rolling(window=self.kc_period).mean()
            
            # Keltner Channels
            df['kc_middle'] = df['close'].rolling(window=self.kc_period).mean()
            df['kc_upper'] = df['kc_middle'] + (self.kc_multiplier * df['atr'])
            df['kc_lower'] = df['kc_middle'] - (self.kc_multiplier * df['atr'])
            
            # TTM Squeeze condition
            df['squeeze_on'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
            
            # TTM Histogram (momentum oscillator)
            df['ttm_histogram'] = self._calculate_ttm_histogram(df)
            
            # Enhanced momentum indicators
            df['ema5'] = df['close'].ewm(span=5).mean()
            df['ema8'] = df['close'].ewm(span=8).mean()
            df['ema21'] = df['close'].ewm(span=21).mean()
            
            # Price momentum
            df['momentum'] = df['close'] - df['close'].shift(self.momentum_lookback)
            df['ema8_momentum'] = df['ema8'] - df['ema8'].shift(self.momentum_lookback)
            
            # Volume analysis
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # Clean up temporary columns
            temp_cols = ['prev_close', 'tr1', 'tr2', 'tr3', 'true_range', 'bb_std']
            df.drop(columns=[col for col in temp_cols if col in df.columns], inplace=True)
            
            return df.dropna()
            
        except Exception as e:
            self.logger.error(f"Error calculating TTM indicators: {e}")
            return df
    
    def _calculate_ttm_histogram(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate TTM Histogram using linear regression slope
        """
        def linear_regression_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            try:
                slope = np.polyfit(x, y, 1)[0]
                return slope
            except:
                return 0
        
        return df['close'].rolling(window=self.hist_lookback).apply(linear_regression_slope, raw=False)
    
    def detect_ttm_squeeze_signal(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Detect TTM Squeeze breakout signals with enhanced pattern recognition
        """
        if df.empty or len(df) < 10:
            return None
            
        try:
            current = df.iloc[-1]
            recent = df.iloc[-7:]  # Last 7 bars for pattern analysis
            
            # Core TTM Squeeze conditions
            squeeze_conditions = self._check_squeeze_conditions(df, current)
            if not squeeze_conditions['valid']:
                return None
            
            # Enhanced pattern recognition
            pattern_analysis = self._analyze_breakout_pattern(recent)
            if not pattern_analysis['valid']:
                return None
            
            # Volume and momentum confirmation
            confirmation = self._check_confirmation_signals(df, current)
            
            # Calculate signal strength
            signal_strength = self._calculate_signal_strength(squeeze_conditions, pattern_analysis, confirmation)
            
            if signal_strength < 0.6:  # Minimum confidence threshold
                return None
            
            # Determine direction
            direction = self._determine_signal_direction(recent, current)
            
            return {
                'signal_type': 'ttm_squeeze_breakout',
                'direction': direction,
                'strength': signal_strength,
                'entry_price': float(current['close']),
                'squeeze_conditions': squeeze_conditions,
                'pattern_analysis': pattern_analysis,
                'confirmation': confirmation,
                'timestamp': current.name,
                'volume_ratio': float(current.get('volume_ratio', 1.0)),
                'atr': float(current.get('atr', 0)),
                'histogram_value': float(current.get('ttm_histogram', 0))
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting TTM squeeze signal: {e}")
            return None
    
    def _check_squeeze_conditions(self, df: pd.DataFrame, current: pd.Series) -> Dict[str, Any]:
        """Check core TTM Squeeze conditions"""
        try:
            # Current squeeze state
            is_squeezed = bool(current.get('squeeze_on', False))
            
            # Recent squeeze history (was in squeeze recently)
            recent_squeeze = df['squeeze_on'].iloc[-10:].any()
            
            # Squeeze release (was squeezed, now breaking out)
            squeeze_release = recent_squeeze and not is_squeezed
            
            return {
                'valid': squeeze_release or is_squeezed,
                'is_squeezed': is_squeezed,
                'recent_squeeze': recent_squeeze,
                'squeeze_release': squeeze_release
            }
        except Exception as e:
            self.logger.error(f"Error checking squeeze conditions: {e}")
            return {'valid': False}
    
    def _analyze_breakout_pattern(self, recent_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze histogram pattern for breakout confirmation"""
        try:
            hist = recent_df['ttm_histogram']
            
            if len(hist) < 3:
                return {'valid': False}
            
            # Check for histogram build pattern
            last_3 = hist.iloc[-3:]
            is_building = (last_3.iloc[-1] > last_3.iloc[-2] > last_3.iloc[-3])
            
            # Check for momentum acceleration
            hist_momentum = hist.diff()
            accelerating = hist_momentum.iloc[-1] > hist_momentum.iloc[-2]
            
            # Pattern strength
            pattern_strength = 0.0
            if is_building:
                pattern_strength += 0.5
            if accelerating:
                pattern_strength += 0.3
            
            return {
                'valid': pattern_strength > 0.3,
                'is_building': is_building,
                'accelerating': accelerating,
                'strength': pattern_strength
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing breakout pattern: {e}")
            return {'valid': False}
    
    def _check_confirmation_signals(self, df: pd.DataFrame, current: pd.Series) -> Dict[str, Any]:
        """Check additional confirmation signals"""
        try:
            confirmations = {}
            
            # EMA alignment
            ema5 = current.get('ema5', 0)
            ema8 = current.get('ema8', 0)
            ema21 = current.get('ema21', 0)
            close = current.get('close', 0)
            
            confirmations['price_above_ema5'] = close > ema5
            confirmations['ema_alignment'] = ema5 > ema8 > ema21
            
            # Momentum confirmation
            momentum = current.get('momentum', 0)
            ema8_momentum = current.get('ema8_momentum', 0)
            
            confirmations['positive_momentum'] = momentum > 0
            confirmations['ema8_rising'] = ema8_momentum > 0
            
            # Volume confirmation
            volume_ratio = current.get('volume_ratio', 1.0)
            confirmations['volume_surge'] = volume_ratio > 1.2
            
            # Calculate confirmation score
            score = sum([1 for conf in confirmations.values() if conf]) / len(confirmations)
            
            return {
                'score': score,
                'details': confirmations
            }
            
        except Exception as e:
            self.logger.error(f"Error checking confirmation signals: {e}")
            return {'score': 0.0, 'details': {}}
    
    def _calculate_signal_strength(self, squeeze_cond: Dict, pattern: Dict, confirmation: Dict) -> float:
        """Calculate overall signal strength"""
        try:
            strength = 0.0
            
            # Base strength from squeeze conditions
            if squeeze_cond.get('squeeze_release'):
                strength += 0.4
            elif squeeze_cond.get('is_squeezed'):
                strength += 0.2
            
            # Pattern strength
            strength += pattern.get('strength', 0.0) * 0.4
            
            # Confirmation strength
            strength += confirmation.get('score', 0.0) * 0.4
            
            return min(strength, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating signal strength: {e}")
            return 0.0
    
    def _determine_signal_direction(self, recent_df: pd.DataFrame, current: pd.Series) -> str:
        """Determine signal direction (long/short)"""
        try:
            # Use histogram direction
            hist_value = current.get('ttm_histogram', 0)
            
            # Use momentum
            momentum = current.get('momentum', 0)
            
            # Use EMA position
            close = current.get('close', 0)
            ema8 = current.get('ema8', 0)
            
            bullish_signals = sum([
                hist_value > 0,
                momentum > 0,
                close > ema8
            ])
            
            return 'long' if bullish_signals >= 2 else 'short'
            
        except Exception as e:
            self.logger.error(f"Error determining signal direction: {e}")
            return 'long'  # Default to long
    
    async def scan_symbol(self, symbol: str, timeframe: str = "5min") -> Optional[Signal]:
        """
        Scan a single symbol for TTM Squeeze signals
        """
        try:
            # Fetch data
            df = await self.fetch_intraday_data(symbol, timeframe)
            if df.empty:
                return None
            
            # Calculate indicators
            df = self.calculate_ttm_indicators(df)
            if df.empty:
                return None
            
            # Detect signal
            signal_data = self.detect_ttm_squeeze_signal(df)
            if not signal_data:
                return None
            
            # Create Signal object
            signal = Signal(
                symbol=symbol,
                side=OrderSide.BUY if signal_data['direction'] == 'long' else OrderSide.SELL,
                entry_price=Decimal(str(signal_data['entry_price'])),
                confidence=signal_data['strength'],
                strategy="ttm_squeeze_enhanced",
                timeframe=timeframe,
                metadata={
                    'signal_type': signal_data['signal_type'],
                    'squeeze_conditions': signal_data['squeeze_conditions'],
                    'pattern_analysis': signal_data['pattern_analysis'],
                    'confirmation': signal_data['confirmation'],
                    'volume_ratio': signal_data['volume_ratio'],
                    'atr': signal_data['atr'],
                    'histogram_value': signal_data['histogram_value']
                }
            )
            
            self.logger.info(f"TTM Squeeze signal detected for {symbol}: {signal_data['direction']} at {signal_data['entry_price']}")
            return signal
            
        except Exception as e:
            self.logger.error(f"Error scanning {symbol}: {e}")
            return None
    
    async def scan_multiple_symbols(self, symbols: List[str], timeframe: str = "5min", max_concurrent: int = 10) -> List[Signal]:
        """
        Scan multiple symbols concurrently for TTM Squeeze signals
        """
        signals = []
        
        # Process in batches to avoid overwhelming the API
        for i in range(0, len(symbols), max_concurrent):
            batch = symbols[i:i + max_concurrent]
            
            tasks = [self.scan_symbol(symbol, timeframe) for symbol in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Signal):
                    signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error in batch scan: {result}")
            
            # Small delay between batches
            if i + max_concurrent < len(symbols):
                await asyncio.sleep(0.1)
        
        self.logger.info(f"TTM Squeeze scan completed: {len(signals)} signals found from {len(symbols)} symbols")
        return signals
