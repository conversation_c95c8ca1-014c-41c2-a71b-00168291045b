[{"C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\HollyChat.js": "3", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AIFeatures.js": "4", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AtlasInterface.js": "5", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\SpaceBackground.js": "6", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\SignalsList.js": "8", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\PositionsList.js": "9", "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AccountInfo.js": "10"}, {"size": 254, "mtime": *************, "results": "11", "hashOfConfig": "12"}, {"size": 5106, "mtime": *************, "results": "13", "hashOfConfig": "12"}, {"size": 14213, "mtime": *************, "results": "14", "hashOfConfig": "12"}, {"size": 13220, "mtime": *************, "results": "15", "hashOfConfig": "12"}, {"size": 12611, "mtime": *************, "results": "16", "hashOfConfig": "12"}, {"size": 3905, "mtime": *************, "results": "17", "hashOfConfig": "12"}, {"size": 650, "mtime": *************, "results": "18", "hashOfConfig": "12"}, {"size": 916, "mtime": *************, "results": "19", "hashOfConfig": "12"}, {"size": 936, "mtime": *************, "results": "20", "hashOfConfig": "12"}, {"size": 1518, "mtime": *************, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "g0bwiv", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\App.js", ["52", "53"], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\HollyChat.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AIFeatures.js", ["54"], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AtlasInterface.js", ["55"], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\SpaceBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\SignalsList.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\PositionsList.js", [], [], "C:\\Users\\<USER>\\Desktop\\CHatbotfinal\\frontend\\src\\components\\AccountInfo.js", [], [], {"ruleId": "56", "severity": 1, "message": "57", "line": 10, "column": 3, "nodeType": "58", "messageId": "59", "endLine": 10, "endColumn": 7}, {"ruleId": "56", "severity": 1, "message": "60", "line": 11, "column": 3, "nodeType": "58", "messageId": "59", "endLine": 11, "endColumn": 14}, {"ruleId": "56", "severity": 1, "message": "61", "line": 1, "column": 27, "nodeType": "58", "messageId": "59", "endLine": 1, "endColumn": 36}, {"ruleId": "56", "severity": 1, "message": "62", "line": 4, "column": 28, "nodeType": "58", "messageId": "59", "endLine": 4, "endColumn": 37}, "no-unused-vars", "'Card' is defined but never used.", "Identifier", "unusedVar", "'CardContent' is defined but never used.", "'useEffect' is defined but never used.", "'BarChart3' is defined but never used."]