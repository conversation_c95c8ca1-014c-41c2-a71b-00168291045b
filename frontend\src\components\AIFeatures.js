import React, { useState, useEffect } from 'react';
import './AIFeatures.css';

const AIFeatures = () => {
  const [activeTab, setActiveTab] = useState('regime');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});
  const [error, setError] = useState(null);

  // Form states
  const [regimeSymbols, setRegimeSymbols] = useState('SPY,QQQ,VIX');
  const [sentimentSymbol, setSentimentSymbol] = useState('AAPL');
  const [sentimentHours, setSentimentHours] = useState(24);
  const [embeddingSymbol, setEmbeddingSymbol] = useState('AAPL');
  const [embeddingContext, setEmbeddingContext] = useState('SPY,QQQ,VIX,TLT');
  const [ttmSymbols, setTtmSymbols] = useState('AAPL,TSLA,NVDA');
  const [ttmTimeframe, setTtmTimeframe] = useState('5min');
  const [newsQuery, setNewsQuery] = useState('market news today');

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:8000';

  const analyzeRegime = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/holly/ai/regime?symbols=${regimeSymbols}`);
      const data = await response.json();
      setResults(prev => ({ ...prev, regime: data }));
    } catch (err) {
      setError('Failed to analyze market regime');
    } finally {
      setLoading(false);
    }
  };

  const analyzeSentiment = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/holly/ai/sentiment/${sentimentSymbol}?hours_back=${sentimentHours}`);
      const data = await response.json();
      setResults(prev => ({ ...prev, sentiment: data }));
    } catch (err) {
      setError('Failed to analyze sentiment');
    } finally {
      setLoading(false);
    }
  };

  const generateEmbeddings = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/holly/ai/embeddings/${embeddingSymbol}?context=${embeddingContext}`);
      const data = await response.json();
      setResults(prev => ({ ...prev, embeddings: data }));
    } catch (err) {
      setError('Failed to generate embeddings');
    } finally {
      setLoading(false);
    }
  };

  const scanTTM = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/holly/ai/ttm-scan?symbols=${ttmSymbols}&timeframe=${ttmTimeframe}`);
      const data = await response.json();
      setResults(prev => ({ ...prev, ttm: data }));
    } catch (err) {
      setError('Failed to scan TTM signals');
    } finally {
      setLoading(false);
    }
  };

  const searchNews = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/holly/ai/news-search?query=${encodeURIComponent(newsQuery)}`);
      const data = await response.json();
      setResults(prev => ({ ...prev, news: data }));
    } catch (err) {
      setError('Failed to search news');
    } finally {
      setLoading(false);
    }
  };

  const renderRegimeTab = () => (
    <div className="ai-tab-content">
      <h3>🌊 Market Regime Detection</h3>
      <p>Analyze current market conditions and regime classification</p>
      
      <div className="input-group">
        <label>Symbols to Analyze:</label>
        <input
          type="text"
          value={regimeSymbols}
          onChange={(e) => setRegimeSymbols(e.target.value)}
          placeholder="SPY,QQQ,VIX"
        />
      </div>
      
      <button onClick={analyzeRegime} disabled={loading} className="ai-button">
        {loading ? 'Analyzing...' : 'Analyze Market Regime'}
      </button>
      
      {results.regime && (
        <div className="results-section">
          <h4>Regime Analysis Results</h4>
          <div className="regime-summary">
            {results.regime.regime_analysis?.market_regime_analysis && (
              <>
                <div className="regime-item">
                  <strong>Current Regime:</strong> {results.regime.regime_analysis.market_regime_analysis.current_regime}
                </div>
                <div className="regime-item">
                  <strong>Confidence:</strong> {(results.regime.regime_analysis.market_regime_analysis.confidence * 100).toFixed(1)}%
                </div>
                <div className="regime-item">
                  <strong>Duration:</strong> {results.regime.regime_analysis.market_regime_analysis.regime_duration_days} days
                </div>
              </>
            )}
          </div>
          <div className="holly-interpretation">
            <h5>Holly's Analysis:</h5>
            <p>{results.regime.holly_interpretation}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderSentimentTab = () => (
    <div className="ai-tab-content">
      <h3>📱 Social Sentiment Analysis</h3>
      <p>Analyze crowd mood from social media platforms</p>
      
      <div className="input-group">
        <label>Symbol:</label>
        <input
          type="text"
          value={sentimentSymbol}
          onChange={(e) => setSentimentSymbol(e.target.value)}
          placeholder="AAPL"
        />
      </div>
      
      <div className="input-group">
        <label>Hours Back:</label>
        <select value={sentimentHours} onChange={(e) => setSentimentHours(e.target.value)}>
          <option value={6}>6 hours</option>
          <option value={12}>12 hours</option>
          <option value={24}>24 hours</option>
          <option value={48}>48 hours</option>
        </select>
      </div>
      
      <button onClick={analyzeSentiment} disabled={loading} className="ai-button">
        {loading ? 'Analyzing...' : 'Analyze Sentiment'}
      </button>
      
      {results.sentiment && (
        <div className="results-section">
          <h4>Sentiment Analysis Results</h4>
          <div className="sentiment-summary">
            {results.sentiment.sentiment_analysis?.analysis_summary && (
              <>
                <div className="sentiment-item">
                  <strong>Overall Sentiment:</strong> {results.sentiment.sentiment_analysis.analysis_summary.overall_sentiment}
                </div>
                <div className="sentiment-item">
                  <strong>Crowd Mood:</strong> {results.sentiment.sentiment_analysis.analysis_summary.crowd_mood}
                </div>
                <div className="sentiment-item">
                  <strong>Posts Analyzed:</strong> {results.sentiment.sentiment_analysis.analysis_summary.total_posts_analyzed}
                </div>
              </>
            )}
          </div>
          <div className="holly-interpretation">
            <h5>Holly's Insights:</h5>
            <p>{results.sentiment.holly_insights}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderEmbeddingsTab = () => (
    <div className="ai-tab-content">
      <h3>🧠 Market Embeddings</h3>
      <p>Advanced pattern matching and market context analysis</p>
      
      <div className="input-group">
        <label>Primary Symbol:</label>
        <input
          type="text"
          value={embeddingSymbol}
          onChange={(e) => setEmbeddingSymbol(e.target.value)}
          placeholder="AAPL"
        />
      </div>
      
      <div className="input-group">
        <label>Context Symbols:</label>
        <input
          type="text"
          value={embeddingContext}
          onChange={(e) => setEmbeddingContext(e.target.value)}
          placeholder="SPY,QQQ,VIX,TLT"
        />
      </div>
      
      <button onClick={generateEmbeddings} disabled={loading} className="ai-button">
        {loading ? 'Generating...' : 'Generate Embeddings'}
      </button>
      
      {results.embeddings && (
        <div className="results-section">
          <h4>Embedding Analysis Results</h4>
          <div className="embeddings-summary">
            {results.embeddings.embedding_analysis?.pattern_analysis && (
              <>
                <div className="embedding-item">
                  <strong>Similar Patterns:</strong> {results.embeddings.embedding_analysis.pattern_analysis.similar_patterns_found}
                </div>
                <div className="embedding-item">
                  <strong>Pattern Confidence:</strong> {(results.embeddings.embedding_analysis.pattern_analysis.pattern_confidence * 100).toFixed(1)}%
                </div>
              </>
            )}
          </div>
          <div className="holly-interpretation">
            <h5>Holly's Insights:</h5>
            <p>{results.embeddings.holly_insights}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderTTMTab = () => (
    <div className="ai-tab-content">
      <h3>📈 TTM Squeeze Scanner</h3>
      <p>AI-enhanced TTM Squeeze pattern recognition</p>
      
      <div className="input-group">
        <label>Symbols to Scan:</label>
        <input
          type="text"
          value={ttmSymbols}
          onChange={(e) => setTtmSymbols(e.target.value)}
          placeholder="AAPL,TSLA,NVDA"
        />
      </div>
      
      <div className="input-group">
        <label>Timeframe:</label>
        <select value={ttmTimeframe} onChange={(e) => setTtmTimeframe(e.target.value)}>
          <option value="1min">1 minute</option>
          <option value="5min">5 minutes</option>
          <option value="15min">15 minutes</option>
          <option value="30min">30 minutes</option>
          <option value="1hour">1 hour</option>
        </select>
      </div>
      
      <button onClick={scanTTM} disabled={loading} className="ai-button">
        {loading ? 'Scanning...' : 'Scan TTM Signals'}
      </button>
      
      {results.ttm && (
        <div className="results-section">
          <h4>TTM Scan Results</h4>
          <div className="ttm-summary">
            {results.ttm.scan_results?.signals_found !== undefined && (
              <div className="ttm-item">
                <strong>Signals Found:</strong> {results.ttm.scan_results.signals_found}
              </div>
            )}
          </div>
          <div className="holly-interpretation">
            <h5>Holly's Analysis:</h5>
            <p>{results.ttm.holly_analysis}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderNewsTab = () => (
    <div className="ai-tab-content">
      <h3>🔍 News Search</h3>
      <p>Search current market news and events</p>
      
      <div className="input-group">
        <label>Search Query:</label>
        <input
          type="text"
          value={newsQuery}
          onChange={(e) => setNewsQuery(e.target.value)}
          placeholder="Apple earnings, Fed meeting, market news"
        />
      </div>
      
      <button onClick={searchNews} disabled={loading} className="ai-button">
        {loading ? 'Searching...' : 'Search News'}
      </button>
      
      {results.news && (
        <div className="results-section">
          <h4>News Search Results</h4>
          <div className="news-summary">
            {results.news.news_results?.total_results && (
              <div className="news-item">
                <strong>Results Found:</strong> {results.news.news_results.total_results}
              </div>
            )}
          </div>
          <div className="holly-interpretation">
            <h5>Holly's Analysis:</h5>
            <p>{results.news.holly_analysis}</p>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="ai-features-container">
      <div className="ai-header">
        <h2>🤖 Holly AI Advanced Features</h2>
        <p>Explore Holly's advanced AI capabilities for market analysis</p>
      </div>

      <div className="ai-tabs">
        <button 
          className={`tab-button ${activeTab === 'regime' ? 'active' : ''}`}
          onClick={() => setActiveTab('regime')}
        >
          🌊 Regime
        </button>
        <button 
          className={`tab-button ${activeTab === 'sentiment' ? 'active' : ''}`}
          onClick={() => setActiveTab('sentiment')}
        >
          📱 Sentiment
        </button>
        <button 
          className={`tab-button ${activeTab === 'embeddings' ? 'active' : ''}`}
          onClick={() => setActiveTab('embeddings')}
        >
          🧠 Embeddings
        </button>
        <button 
          className={`tab-button ${activeTab === 'ttm' ? 'active' : ''}`}
          onClick={() => setActiveTab('ttm')}
        >
          📈 TTM Scan
        </button>
        <button 
          className={`tab-button ${activeTab === 'news' ? 'active' : ''}`}
          onClick={() => setActiveTab('news')}
        >
          🔍 News
        </button>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="ai-content">
        {activeTab === 'regime' && renderRegimeTab()}
        {activeTab === 'sentiment' && renderSentimentTab()}
        {activeTab === 'embeddings' && renderEmbeddingsTab()}
        {activeTab === 'ttm' && renderTTMTab()}
        {activeTab === 'news' && renderNewsTab()}
      </div>
    </div>
  );
};

export default AIFeatures;
